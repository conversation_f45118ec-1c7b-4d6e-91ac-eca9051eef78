
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: create_complete_project_folder.py
# execution: true
import os
import json
import shutil
from datetime import datetime

print("Kapsamlı Alertatron AI Agent proje klasörü oluşturuluyor...")
print("="*80)

# Ana proje klasörünü oluştur
project_folder = "Alertatron_AI_Agent_Project"
if not os.path.exists(project_folder):
    os.makedirs(project_folder)

# Alt klasör yapısını oluştur
folders = [
    "01_Documentation",
    "02_Technical_Specifications", 
    "03_Order_Types_Commands",
    "04_API_Integration",
    "05_Examples_and_Usage",
    "06_Screenshots_and_Visuals",
    "07_Extracted_Data",
    "08_Implementation_Guide"
]

for folder in folders:
    folder_path = os.path.join(project_folder, folder)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

print("1. Klasör yapısı oluşturuldu:")
for folder in folders:
    print(f"   ✅ {folder}")

# 2. Mevcut dosyaları kopyala
print("\n2. Mevcut dosyalar kopyalanıyor...")
files_to_copy = [
    ("Alertatron_Complete_Technical_Documentation.pdf", "01_Documentation"),
    ("extracted_specifications.json", "02_Technical_Specifications"),
    ("alertatron_technical_specifications.txt", "02_Technical_Specifications"),
    ("basic_concepts_20250711-144313_content.json", "07_Extracted_Data"),
    ("command_reference_20250711-144724_content.json", "07_Extracted_Data"),
    ("market_order_20250711-151348_content.json", "07_Extracted_Data"),
    ("limit_order_20250711-151505_content.json", "07_Extracted_Data")
]

copied_count = 0
for file_name, target_folder in files_to_copy:
    if os.path.exists(file_name):
        target_path = os.path.join(project_folder, target_folder)
        shutil.copy(file_name, target_path)
        copied_count += 1
        print(f"   ✅ {file_name} -> {target_folder}")

print(f"   📁 {copied_count} dosya kopyalandı")

# 3. Detaylı emir tipi dokümantasyonu oluştur
print("\n3. Detaylı emir tipi dokümantasyonu oluşturuluyor...")

order_types_data = {
    "market_order": {
        "name": "Market Order (Piyasa Emri)",
        "syntax": "market(side, amount, position, reduceOnly)",
        "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri",
        "parameters": {
            "side": {"type": "string", "required": True, "values": ["buy", "sell"]},
            "amount": {"type": "number/string", "formats": ["1000", "50%", "50%x", "50%p"]},
            "position": {"type": "number/string", "description": "Hedef pozisyon büyüklüğü"},
            "reduceOnly": {"type": "boolean", "description": "Sadece pozisyon azaltma"}
        },
        "examples": ["market(side='buy', amount=1000)", "market(position=0)"]
    },
    "limit_order": {
        "name": "Limit Order (Limit Emri)", 
        "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)",
        "description": "Belirli fiyattan limit emri yerleştirir",
        "parameters": {
            "side": {"type": "string", "required": True},
            "amount": {"type": "number", "required": True},
            "offset": {"type": "string", "formats": ["1%", "50", "e1%", "@50000"]},
            "postOnly": {"type": "boolean", "default": True},
            "tag": {"type": "string", "description": "Emir etiketi"}
        }
    },
    "stop_order": {
        "name": "Stop Order (Stop Emri)",
        "syntax": "stop(side, amount, offset, ...)",
        "description": "Stop seviyesine ulaşıldığında tetiklenen emir",
        "use_cases": ["Stop-loss", "Breakout trading", "Risk yönetimi"]
    },
    "trailing_stop_order": {
        "name": "Trailing Stop Order",
        "syntax": "trailingStop(side, amount, offset, ...)",
        "description": "Fiyat hareketini takip eden stop emri"
    },
    "trailing_take_profit": {
        "name": "Trailing Take Profit",
        "syntax": "trailingTakeProfit(side, amount, offset, ...)",
        "description": "Kar seviyesini takip eden emir"
    },
    "iceberg_order": {
        "name": "Iceberg Order (Buzdağı Emri)",
        "syntax": "iceberg(side, amount, sliceAmount, ...)",
        "description": "Büyük emirleri küçük parçalara bölen emir"
    },
    "grid_order": {
        "name": "Grid Order (Izgara Emri)",
        "syntax": "grid(side, amount, gridLevels, ...)",
        "description": "Fiyat aralıklarında otomatik alım-satım"
    },
    "twap_order": {
        "name": "TWAP Order",
        "syntax": "twap(side, amount, duration, ...)",
        "description": "Zaman ağırlıklı ortalama fiyat emri"
    },
    "ping_pong_order": {
        "name": "Ping Pong Order",
        "syntax": "pingPong(side, amount, range, ...)",
        "description": "Belirli aralıkta sürekli alım-satım"
    },
    "market_maker_order": {
        "name": "Market Maker Order",
        "syntax": "marketMaker(spread, amount, ...)",
        "description": "Spread'den kar elde etme emri"
    },
    "aggressive_entry_order": {
        "name": "Aggressive Entry Order",
        "syntax": "aggressiveEntry(side, amount, ...)",
        "description": "Agresif pozisyon giriş emri"
    },
    "dynamic_take_profit": {
        "name": "Dynamic Take Profit",
        "syntax": "dynamicTakeProfit(side, amount, levels, ...)",
        "description": "Dinamik kar alma emri"
    },
    "one_cancels_other": {
        "name": "One Cancels Other (OCO)",
        "syntax": "oco(order1, order2)",
        "description": "Biri gerçekleşince diğerini iptal eden emir çifti"
    },
    "scaled_order": {
        "name": "Scaled Order",
        "syntax": "scaled(side, totalAmount, levels, ...)",
        "description": "Kademeli fiyat seviyelerinde emir"
    },
    "waiting_limit_order": {
        "name": "Waiting Limit Order",
        "syntax": "waitingLimit(side, amount, condition, ...)",
        "description": "Koşul gerçekleşene kadar bekleyen limit emir"
    }
}

# Emir tiplerini kaydet
order_types_file = os.path.join(project_folder, "03_Order_Types_Commands", "all_order_types.json")
with open(order_types_file, 'w', encoding='utf-8') as f:
    json.dump(order_types_data, f, indent=2, ensure_ascii=False)

print(f"   ✅ {len(order_types_data)} emir tipi detaylandırıldı")

# 4. API entegrasyon rehberi oluştur
print("\n4. API entegrasyon rehberi oluşturuluyor...")

api_integration = {
    "supported_exchanges": [
        {"name": "Binance Spot", "features": ["spot trading"], "notes": "En büyük borsa"},
        {"name": "Binance Futures", "features": ["futures"], "notes": "Sadece One-Way mode"},
        {"name": "Bybit", "features": ["perpetual", "spot"], "notes": "USDT ve Inverse kontratlar"},
        {"name": "OKX", "features": ["multi"], "notes": "Çoklu ticaret türleri"},
        {"name": "BitMEX", "features": ["derivatives"], "notes": "Bitcoin vadeli işlemler"},
        {"name": "Bitfinex", "features": ["spot", "margin"], "notes": "Spot ve margin"},
        {"name": "Bitget", "features": ["multi"], "notes": "Çoklu özellikler"},
        {"name": "Deribit", "features": ["futures", "options"], "notes": "Kripto opsiyonlar"},
        {"name": "Binance US", "features": ["spot"], "notes": "ABD kullanıcıları"}
    ],
    "api_setup_requirements": {
        "api_keys": "Her borsa için ayrı API anahtarları",
        "permissions": "Trading permissions gerekli",
        "ip_restrictions": "Gerekirse IP kısıtlamaları kaldırılmalı",
        "naming": "API anahtarlarına açıklayıcı isimler verilmeli"
    },
    "webhook_format": {
        "basic_structure": "MyKeys(SYMBOL) { command(parameters); } #bot",
        "components": {
            "MyKeys": "API anahtar seti tanımlayıcısı",
            "SYMBOL": "Trading sembolü (ör: BTCUSDT)",
            "command": "Algoritmik trading komutu",
            "bot_tag": "Yönlendirme etiketi"
        }
    }
}

api_file = os.path.join(project_folder, "04_API_Integration", "api_integration_guide.json")
with open(api_file, 'w', encoding='utf-8') as f:
    json.dump(api_integration, f, indent=2, ensure_ascii=False)

# 5. Python implementasyon rehberi oluştur
print("\n5. Python implementasyon rehberi oluşturuluyor...")
