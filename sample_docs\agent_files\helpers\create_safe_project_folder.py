
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: create_safe_project_folder.py
# execution: true
import os
import json
from datetime import datetime

print("Güvenli proje klasörü oluşturuluyor...")
print("="*80)

# Ana proje klasörünü oluştur
project_folder = "Alertatron_AI_Agent_Project"
if not os.path.exists(project_folder):
    os.makedirs(project_folder)

# Alt klasör yapısını oluştur
folders = [
    "01_Documentation",
    "02_Technical_Specifications", 
    "03_Order_Types_Commands",
    "04_API_Integration",
    "05_Examples_and_Usage",
    "06_Screenshots_and_Visuals",
    "07_Extracted_Data",
    "08_Implementation_Guide"
]

for folder in folders:
    folder_path = os.path.join(project_folder, folder)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

print("1. Klasör yapısı oluşturuldu:")
for folder in folders:
    print(f"   ✅ {folder}")

# 2. Detaylı emir tipi dokümantasyonu oluştur
print("\n2. Detaylı emir tipi dokümantasyonu oluşturuluyor...")

order_types_data = {
    "market_order": {
        "name": "Market Order (Piyasa Emri)",
        "syntax": "market(side, amount, position, reduceOnly)",
        "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri. Emir borsaya yerleştirilir yerleştirilmez tamamlanır.",
        "parameters": {
            "side": {
                "type": "string",
                "required": True,
                "description": "Alım/Satım yönü",
                "values": ["buy", "sell"],
                "default": "buy"
            },
            "amount": {
                "type": "number/string",
                "required": "position kullanılmıyorsa zorunlu",
                "description": "İşlem miktarı - çeşitli formatları destekler",
                "formats": {
                    "absolute": "amount=1000 (1000 kontrat)",
                    "percentage": "amount=50% (bakiye yüzdesi)",
                    "available": "amount=50%x (mevcut bakiye yüzdesi)",
                    "position": "amount=50%p (pozisyon büyüklüğü yüzdesi)",
                    "balance": "amount=50%b (bakiye yüzdesi ile aynı)",
                    "available_alt": "amount=50%a (mevcut bakiye yüzdesi ile aynı)"
                }
            },
            "position": {
                "type": "number/string",
                "required": False,
                "description": "Hedef pozisyon büyüklüğü - kullanıldığında side ve amount göz ardı edilir",
                "examples": [
                    "position=1000 (1000 kontrat long pozisyon)",
                    "position=0 (pozisyonu kapat)",
                    "position=-500 (500 kontrat short pozisyon)",
                    "position=50% (bakiye yüzdesinde pozisyon)"
                ]
            },
            "reduceOnly": {
                "type": "boolean",
                "required": False,
                "description": "Sadece mevcut pozisyonu azaltmak için kullanılır"
            }
        },
        "examples": [
            "market(side='buy', amount=1000)",
            "market(side='sell', amount='50%')",
            "market(position=1000)",
            "market(position=0)  // Pozisyonu kapat"
        ]
    },
    "limit_order": {
        "name": "Limit Order (Limit Emri)",
        "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)",
        "description": "Standart limit emri yerleştirir. Emir borsaya yerleştirilir yerleştirilmez tamamlanır ve doldurulmasını beklemez.",
        "parameters": {
            "side": {
                "type": "string",
                "required": True,
                "description": "Emir yönü",
                "values": ["buy", "sell"]
            },
            "amount": {
                "type": "number",
                "required": "position kullanılmıyorsa zorunlu",
                "default": 0,
                "description": "İşlem miktarı - market order ile aynı formatları destekler"
            },
            "offset": {
                "type": "number/string",
                "required": True,
                "default": "1%",
                "description": "Fiyat ofseti - güncel fiyat veya ortalama giriş fiyatından",
                "formats": {
                    "absolute": "offset=50 ($50 ofset)",
                    "percentage": "offset=1% (%1 ofset)",
                    "entry_absolute": "offset=e50 (giriş fiyatından $50)",
                    "entry_percentage": "offset=e1% (giriş fiyatından %1)",
                    "absolute_price": "offset=@50000 (mutlak fiyat $50,000)"
                }
            },
            "postOnly": {
                "type": "boolean",
                "required": False,
                "default": True,
                "description": "Sadece post emri olarak gönder"
            },
            "reduceOnly": {
                "type": "boolean",
                "required": False,
                "description": "Sadece pozisyon azaltma"
            },
            "position": {
                "type": "string/number",
                "required": False,
                "description": "Pozisyon tabanlı emir boyutlandırma"
            },
            "tag": {
                "type": "string",
                "required": False,
                "description": "Emir tanımlayıcı etiketi"
            }
        },
        "examples": [
            "limit('buy', 1000, '1%', true)",
            "limit('sell', '50%', 'e1%', true)",
            "limit('buy', '25%x', '@50000', false)"
        ]
    },
    "stop_order": {
        "name": "Stop Order (Stop Emri)",
        "syntax": "stop(side, amount, offset, postOnly, reduceOnly, position, tag)",
        "description": "Stop emri - belirli bir fiyat seviyesine ulaşıldığında tetiklenen emir",
        "parameters": "Limit order ile benzer parametreler",
        "use_cases": [
            "Stop-loss emirleri",
            "Breakout stratejileri",
            "Risk yönetimi"
        ]
    },
    "trailing_stop_order": {
        "name": "Trailing Stop Order (Takip Eden Stop)",
        "syntax": "trailingStop(side, amount, offset, ...)",
        "description": "Fiyat lehte hareket ettikçe stop seviyesini otomatik olarak takip eden emir",
        "benefits": [
            "Kar koruma",
            "Trend takibi",
            "Otomatik risk yönetimi"
        ]
    },
    "trailing_take_profit": {
        "name": "Trailing Take Profit (Takip Eden Kar Al)",
        "syntax": "trailingTakeProfit(side, amount, offset, ...)",
        "description": "Kar seviyesini fiyat hareketlerine göre otomatik olarak ayarlayan emir"
    },
    "iceberg_order": {
        "name": "Iceberg Order (Buzdağı Emri)",
        "syntax": "iceberg(side, amount, sliceAmount, ...)",
        "description": "Büyük emirleri küçük parçalara bölerek piyasaya sunan algoritmik emir",
        "purpose": "Büyük emirlerin piyasa etkisini azaltma"
    },
    "grid_order": {
        "name": "Grid Order (Izgara Emri)",
        "syntax": "grid(side, amount, gridLevels, ...)",
        "description": "Belirli fiyat aralıklarında otomatik alım-satım yapan algoritmik emir",
        "strategy": "Range trading ve volatilite ticareti"
    },
    "twap_order": {
        "name": "TWAP Order (Zaman Ağırlıklı Ortalama Fiyat)",
        "syntax": "twap(side, amount, duration, ...)",
        "description": "Belirli bir zaman diliminde emirleri eşit aralıklarla bölerek icra eden emir",
        "advantage": "Piyasa etkisini minimize etme"
    },
    "ping_pong_order": {
        "name": "Ping Pong Order",
        "syntax": "pingPong(side, amount, range, ...)",
        "description": "Belirli fiyat aralığında alım-satım yaparak kar elde etmeyi hedefleyen emir"
    },
    "market_maker_order": {
        "name": "Market Maker Order",
        "syntax": "marketMaker(spread, amount, ...)",
        "description": "Alış-satış emirlerini eşzamanlı olarak vererek spread'den kar elde etme emri"
    },
    "aggressive_entry_order": {
        "name": "Aggressive Entry Order",
        "syntax": "aggressiveEntry(side, amount, ...)",
        "description": "Pozisyona hızlı giriş için agresif emir stratejisi"
    },
    "dynamic_take_profit": {
        "name": "Dynamic Take Profit",
        "syntax": "dynamicTakeProfit(side, amount, levels, ...)",
        "description": "Piyasa koşullarına göre dinamik olarak ayarlanan kar alma emri"
    },
    "one_cancels_other": {
        "name": "One Cancels Other (OCO)",
        "syntax": "oco(order1, order2)",
        "description": "İki emirden biri gerçekleştiğinde diğerini iptal eden emir çifti"
    },
    "scaled_order": {
        "name": "Scaled Order (Kademeli Emir)",
        "syntax": "scaled(side, totalAmount, levels, ...)",
        "description": "Toplam miktarı farklı fiyat seviyelerine bölen kademeli emir"
    },
    "waiting_limit_order": {
        "name": "Waiting Limit Order",
        "syntax": "waitingLimit(side, amount, condition, ...)",
        "description": "Belirli koşullar gerçekleşene kadar bekleyen limit emir"
    }
}

# Emir tipi dokümantasyonunu JSON olarak kaydet
order_types_file = os.path.join(project_folder, "03_Order_Types_Commands", "all_order_types_detailed.json")
with open(order_types_file, 'w', encoding='utf-8') as f:
    json.dump(order_types_data, f, indent=2, ensure_ascii=False)

print(f"   ✅ Detaylı emir tipi dokümantasyonu: {len(order_types_data)} emir tipi")

# 3. Python implementasyon rehberi oluştur
print("\n3. Python implementasyon rehberi oluşturuluyor...")
