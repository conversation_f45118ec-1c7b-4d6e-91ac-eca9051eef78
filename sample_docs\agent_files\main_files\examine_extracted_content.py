
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: examine_extracted_content.py  
# execution: true
import json
import os

print("Examining the actual content of extracted JSON files...")
print("="*80)

# Load one of the JSON files to understand its structure
sample_file = "./basic_concepts_20250711-144313_content.json"

if os.path.exists(sample_file):
    with open(sample_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Structure of {sample_file}:")
    print(f"Keys available: {list(data.keys())}")
    print()
    
    # Show sample of links structure
    if 'links' in data and data['links']:
        print("Sample link structure:")
        print(f"First link: {data['links'][0]}")
        print()
    
    # Show sample of images structure  
    if 'images' in data and data['images']:
        print("Sample image structure:")
        print(f"First image: {data['images'][0]}")
        print()
    
    # Check if there's actual extracted text content
    if 'extracted_text' in data:
        print("Extracted text content found:")
        print(data['extracted_text'][:500] + "..." if len(data['extracted_text']) > 500 else data['extracted_text'])
        print()
    
    # Check all available keys and their types
    print("All available data keys and types:")
    for key, value in data.items():
        print(f"- {key}: {type(value)} (length: {len(value) if hasattr(value, '__len__') else 'N/A'})")

else:
    print(f"File {sample_file} not found")

print("="*80)
print("Content structure examination completed.")