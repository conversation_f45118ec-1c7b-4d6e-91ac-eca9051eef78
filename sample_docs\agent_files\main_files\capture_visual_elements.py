
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: capture_visual_elements.py
# execution: true
from api_server.agent_tools.screenshot_webpage import screenshot_webpage

# Capture visual elements from the Alertatron basic concepts page
url = "https://alertatron.com/docs/automated-trading/basic-concepts"

print("Capturing visual elements from Alertatron documentation...")
print("="*70)

# Capture screenshots with multiple page downs to get complete content
screenshots, links, images = screenshot_webpage(
    url=url,
    page_downs=5,  # Ensure we capture all content
    max_page_downs=5
)

print("="*70)
print(f"Visual capture completed:")
print(f"- Screenshots captured: {len(screenshots)}")
print(f"- Links found: {len(links)}")  
print(f"- Images found: {len(images)}")

# Display screenshot file names
if screenshots:
    print("\nScreenshot files:")
    for i, screenshot in enumerate(screenshots):
        print(f"  {i+1}. {screenshot}")

# Display image information
if images:
    print("\nImage information:")
    for i, image in enumerate(images):
        print(f"  {i+1}. {image}")

print("="*70)
print("Visual documentation capture completed. Proceeding to explore additional URLs...")