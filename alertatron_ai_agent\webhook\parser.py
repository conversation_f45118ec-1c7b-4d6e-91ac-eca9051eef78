"""
Alertatron command parser for webhook messages.
"""

import re
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel

from ..utils.logger import get_logger


logger = get_logger("command_parser")


class ParsedCommand(BaseModel):
    """Parsed command data structure."""
    
    key_name: str
    symbol: str
    command: str
    parameters: Dict[str, Any]
    bot_tag: str
    raw_message: str


class CommandParser:
    """Parses Alertatron webhook commands into structured data."""
    
    def __init__(self):
        # Main pattern for Alertatron format
        self.main_pattern = re.compile(
            r'(\w+)\(([A-Z0-9]+)\)\s*\{\s*(\w+)\(([^)]*)\);\s*\}\s*#(\w+)',
            re.IGNORECASE
        )
        
        # Parameter parsing patterns
        self.param_patterns = {
            'key_value': re.compile(r'(\w+)=([^,]+)'),
            'quoted_string': re.compile(r"'([^']*)'|\"([^\"]*)\""),
            'number': re.compile(r'^-?\d+\.?\d*$'),
            'percentage': re.compile(r'^(\d+\.?\d*)%([xpba]?)$'),
            'boolean': re.compile(r'^(true|false)$', re.IGNORECASE)
        }
    
    def parse_webhook_message(self, message: str) -> Optional[ParsedCommand]:
        """
        Parse a webhook message into structured command data.
        
        Args:
            message: Raw webhook message
            
        Returns:
            ParsedCommand object or None if parsing fails
        """
        try:
            # Extract main components
            match = self.main_pattern.match(message.strip())
            if not match:
                logger.error("Failed to match main pattern", message=message)
                return None
            
            key_name, symbol, command, param_string, bot_tag = match.groups()
            
            # Parse parameters
            parameters = self._parse_parameters(param_string)
            
            return ParsedCommand(
                key_name=key_name,
                symbol=symbol.upper(),
                command=command.lower(),
                parameters=parameters,
                bot_tag=bot_tag.lower(),
                raw_message=message
            )
        
        except Exception as e:
            logger.error("Error parsing webhook message", error=str(e), message=message)
            return None
    
    def _parse_parameters(self, param_string: str) -> Dict[str, Any]:
        """Parse parameter string into dictionary."""
        if not param_string.strip():
            return {}
        
        parameters = {}
        
        # Split by commas, but be careful with quoted strings
        param_parts = self._split_parameters(param_string)
        
        for part in param_parts:
            part = part.strip()
            if not part:
                continue
            
            # Check for key=value format
            kv_match = self.param_patterns['key_value'].match(part)
            if kv_match:
                key, value = kv_match.groups()
                parameters[key] = self._parse_parameter_value(value.strip())
            else:
                # Handle positional parameters (for backward compatibility)
                # This would need more sophisticated logic based on command type
                logger.warning("Unrecognized parameter format", parameter=part)
        
        return parameters
    
    def _split_parameters(self, param_string: str) -> List[str]:
        """Split parameters by comma, respecting quoted strings."""
        parts = []
        current_part = ""
        in_quotes = False
        quote_char = None
        
        for char in param_string:
            if char in ['"', "'"] and not in_quotes:
                in_quotes = True
                quote_char = char
                current_part += char
            elif char == quote_char and in_quotes:
                in_quotes = False
                quote_char = None
                current_part += char
            elif char == ',' and not in_quotes:
                parts.append(current_part)
                current_part = ""
            else:
                current_part += char
        
        if current_part:
            parts.append(current_part)
        
        return parts
    
    def _parse_parameter_value(self, value: str) -> Union[str, int, float, bool]:
        """Parse a parameter value to appropriate type."""
        value = value.strip()
        
        # Remove quotes if present
        quote_match = self.param_patterns['quoted_string'].match(value)
        if quote_match:
            return quote_match.group(1) or quote_match.group(2)
        
        # Check for boolean
        bool_match = self.param_patterns['boolean'].match(value)
        if bool_match:
            return value.lower() == 'true'
        
        # Check for percentage
        percent_match = self.param_patterns['percentage'].match(value)
        if percent_match:
            return value  # Keep as string for now, will be processed by order handlers
        
        # Check for number
        if self.param_patterns['number'].match(value):
            if '.' in value:
                return float(value)
            else:
                return int(value)
        
        # Return as string if no other type matches
        return value
    
    def validate_command_parameters(self, command: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and normalize parameters for a specific command.
        
        This method will be expanded as order types are implemented.
        """
        validated_params = parameters.copy()
        
        # Basic validation for common parameters
        if 'side' in validated_params:
            side = str(validated_params['side']).lower()
            if side not in ['buy', 'sell']:
                logger.warning("Invalid side parameter", side=side)
                validated_params['side'] = 'buy'  # Default to buy
            else:
                validated_params['side'] = side
        
        if 'amount' in validated_params:
            amount = validated_params['amount']
            if isinstance(amount, str) and '%' in amount:
                # Keep percentage strings as-is for now
                pass
            elif isinstance(amount, (int, float)):
                validated_params['amount'] = float(amount)
        
        return validated_params
    
    def get_command_info(self, command: str) -> Dict[str, Any]:
        """Get information about a specific command."""
        # This will be expanded with detailed command information
        command_info = {
            'market': {
                'description': 'Market order execution',
                'required_params': ['side'],
                'optional_params': ['amount', 'position', 'reduceOnly']
            },
            'limit': {
                'description': 'Limit order placement',
                'required_params': ['side', 'offset'],
                'optional_params': ['amount', 'postOnly', 'reduceOnly', 'position', 'tag']
            },
            'stop': {
                'description': 'Stop order placement',
                'required_params': ['side', 'offset'],
                'optional_params': ['amount', 'postOnly', 'reduceOnly', 'position', 'tag']
            }
        }
        
        return command_info.get(command.lower(), {
            'description': f'Order type: {command}',
            'required_params': [],
            'optional_params': []
        })
