"""
Webhook validation module for TradingView signals.
"""

from typing import Dict, Any, Tu<PERSON>, Optional
import re
from pydantic import BaseModel, ValidationError

from ..utils.logger import get_logger
from ..config.api_keys import get_api_key_manager


logger = get_logger("webhook_validator")


class WebhookData(BaseModel):
    """Pydantic model for webhook data validation."""
    
    message: str
    timestamp: Optional[str] = None
    source: Optional[str] = "tradingview"
    
    class Config:
        extra = "allow"


class WebhookValidator:
    """Validates incoming TradingView webhooks."""
    
    def __init__(self):
        self.api_key_manager = get_api_key_manager()
        
        # Regex pattern for Alertatron command format
        # MyKeys(SYMBOL) { command(parameters); } #bot
        self.alertatron_pattern = re.compile(
            r'(\w+)\(([A-Z0-9]+)\)\s*\{\s*(\w+)\(([^)]*)\);\s*\}\s*#(\w+)',
            re.IGNORECASE
        )
        
        # Supported order types
        self.supported_order_types = {
            'market', 'limit', 'stop', 'stoporTakeprofit', 'trailingstop',
            'trailingtakeprofit', 'trailinglimit', 'iceberg', 'grid', 'scaled',
            'twap', 'pingpong', 'marketmaker', 'aggressiveentry',
            'dynamictakeprofit', 'oco', 'waitinglimit', 'managed'
        }
    
    def validate_webhook(self, webhook_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate incoming webhook data.
        
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            # Validate basic structure
            validated_data = WebhookData(**webhook_data)
            
            # Validate message format
            is_format_valid, format_error = self._validate_message_format(validated_data.message)
            if not is_format_valid:
                return False, format_error
            
            # Parse and validate components
            parsed = self._parse_message(validated_data.message)
            if not parsed:
                return False, "Failed to parse webhook message"
            
            key_name, symbol, command, parameters, bot_tag = parsed
            
            # Validate key name exists
            if not self._validate_key_name(key_name):
                return False, f"API key '{key_name}' not found or invalid"
            
            # Validate symbol format
            if not self._validate_symbol(symbol):
                return False, f"Invalid symbol format: {symbol}"
            
            # Validate command
            if not self._validate_command(command):
                return False, f"Unsupported order type: {command}"
            
            # Validate bot tag
            if not self._validate_bot_tag(bot_tag):
                return False, f"Invalid bot tag: {bot_tag}"
            
            logger.info("Webhook validation passed", 
                       key_name=key_name, 
                       symbol=symbol, 
                       command=command)
            
            return True, None
        
        except ValidationError as e:
            return False, f"Validation error: {str(e)}"
        except Exception as e:
            logger.error("Webhook validation error", error=str(e))
            return False, f"Validation failed: {str(e)}"
    
    def _validate_message_format(self, message: str) -> Tuple[bool, Optional[str]]:
        """Validate the basic message format."""
        if not message or not isinstance(message, str):
            return False, "Message is empty or not a string"
        
        if not self.alertatron_pattern.match(message.strip()):
            return False, "Message does not match Alertatron format: MyKeys(SYMBOL) { command(parameters); } #bot"
        
        return True, None
    
    def _parse_message(self, message: str) -> Optional[Tuple[str, str, str, str, str]]:
        """Parse the webhook message into components."""
        match = self.alertatron_pattern.match(message.strip())
        if not match:
            return None
        
        key_name, symbol, command, parameters, bot_tag = match.groups()
        return key_name, symbol, command, parameters, bot_tag
    
    def _validate_key_name(self, key_name: str) -> bool:
        """Validate that the API key name exists."""
        # Check if any exchange has this key name
        available_keys = self.api_key_manager.list_available_keys()
        
        for exchange, keys in available_keys.items():
            if key_name in keys:
                return True
        
        return False
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate symbol format."""
        if not symbol or len(symbol) < 3:
            return False
        
        # Basic symbol validation (alphanumeric, typically 6-12 characters)
        if not re.match(r'^[A-Z0-9]{3,12}$', symbol):
            return False
        
        return True
    
    def _validate_command(self, command: str) -> bool:
        """Validate that the command is a supported order type."""
        return command.lower() in self.supported_order_types
    
    def _validate_bot_tag(self, bot_tag: str) -> bool:
        """Validate bot tag."""
        if not bot_tag:
            return False
        
        # Allow common bot tags
        valid_tags = {'bot', 'alertatron', 'trade', 'signal'}
        return bot_tag.lower() in valid_tags
    
    def get_validation_info(self, message: str) -> Dict[str, Any]:
        """Get detailed validation information for a message."""
        result = {
            "is_valid": False,
            "components": {},
            "errors": [],
            "warnings": []
        }
        
        try:
            # Parse message
            parsed = self._parse_message(message)
            if not parsed:
                result["errors"].append("Failed to parse message format")
                return result
            
            key_name, symbol, command, parameters, bot_tag = parsed
            result["components"] = {
                "key_name": key_name,
                "symbol": symbol,
                "command": command,
                "parameters": parameters,
                "bot_tag": bot_tag
            }
            
            # Validate each component
            if not self._validate_key_name(key_name):
                result["errors"].append(f"API key '{key_name}' not found")
            
            if not self._validate_symbol(symbol):
                result["errors"].append(f"Invalid symbol: {symbol}")
            
            if not self._validate_command(command):
                result["errors"].append(f"Unsupported command: {command}")
            
            if not self._validate_bot_tag(bot_tag):
                result["warnings"].append(f"Unusual bot tag: {bot_tag}")
            
            result["is_valid"] = len(result["errors"]) == 0
            
        except Exception as e:
            result["errors"].append(f"Validation error: {str(e)}")
        
        return result
