"""
Risk calculation and management utilities.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pydantic import BaseModel
import math

from ..utils.logger import get_logger, log_risk_management
from ..config.settings import get_settings


logger = get_logger("risk_calculator")
settings = get_settings()


class RiskMetrics(BaseModel):
    """Risk metrics model."""
    
    position_size: float
    position_value: float
    risk_percentage: float
    max_loss: float
    leverage: float = 1.0
    margin_required: float = 0.0
    liquidation_price: Optional[float] = None


class PortfolioRisk(BaseModel):
    """Portfolio-level risk metrics."""
    
    total_exposure: float
    total_margin_used: float
    available_margin: float
    portfolio_risk: float
    correlation_risk: float
    concentration_risk: float


class RiskCalculator:
    """Calculates various risk metrics for trading positions."""
    
    def __init__(self):
        self.max_position_size = settings.max_position_size
        self.max_daily_loss = settings.max_daily_loss
        self.daily_losses = {}  # Track daily losses by date
    
    def calculate_position_risk(self, 
                              symbol: str,
                              side: str,
                              amount: float,
                              entry_price: float,
                              stop_loss_price: Optional[float] = None,
                              leverage: float = 1.0,
                              account_balance: float = 10000.0) -> RiskMetrics:
        """Calculate risk metrics for a position."""
        try:
            # Calculate position value
            position_value = amount * entry_price
            
            # Calculate maximum loss
            if stop_loss_price:
                if side.lower() == 'buy':
                    max_loss = (entry_price - stop_loss_price) * amount
                else:
                    max_loss = (stop_loss_price - entry_price) * amount
            else:
                # Assume 100% loss if no stop loss
                max_loss = position_value
            
            # Calculate risk percentage
            risk_percentage = (max_loss / account_balance) * 100
            
            # Calculate margin required (for leveraged positions)
            margin_required = position_value / leverage if leverage > 1 else position_value
            
            # Calculate liquidation price (simplified)
            liquidation_price = None
            if leverage > 1:
                if side.lower() == 'buy':
                    liquidation_price = entry_price * (1 - 0.8 / leverage)  # Simplified calculation
                else:
                    liquidation_price = entry_price * (1 + 0.8 / leverage)
            
            risk_metrics = RiskMetrics(
                position_size=amount,
                position_value=position_value,
                risk_percentage=risk_percentage,
                max_loss=max_loss,
                leverage=leverage,
                margin_required=margin_required,
                liquidation_price=liquidation_price
            )
            
            logger.info("Position risk calculated",
                       symbol=symbol,
                       risk_percentage=risk_percentage,
                       max_loss=max_loss,
                       position_value=position_value)
            
            return risk_metrics
        
        except Exception as e:
            logger.error("Risk calculation failed", symbol=symbol, error=str(e))
            raise
    
    def validate_position_size(self, 
                             symbol: str,
                             amount: float,
                             price: float) -> Dict[str, Any]:
        """Validate if position size is within risk limits."""
        try:
            position_value = amount * price
            
            # Check against maximum position size
            if position_value > self.max_position_size:
                return {
                    "valid": False,
                    "reason": "position_size_exceeded",
                    "message": f"Position value {position_value:.2f} exceeds maximum {self.max_position_size:.2f}",
                    "max_allowed_amount": self.max_position_size / price
                }
            
            return {
                "valid": True,
                "position_value": position_value,
                "message": "Position size within limits"
            }
        
        except Exception as e:
            logger.error("Position size validation failed", symbol=symbol, error=str(e))
            return {
                "valid": False,
                "reason": "validation_error",
                "message": str(e)
            }
    
    def check_daily_loss_limit(self, 
                             exchange: str,
                             potential_loss: float) -> Dict[str, Any]:
        """Check if potential loss would exceed daily limit."""
        try:
            today = datetime.now().date()
            daily_key = f"{exchange}_{today}"
            
            current_daily_loss = self.daily_losses.get(daily_key, 0.0)
            total_potential_loss = current_daily_loss + potential_loss
            
            if total_potential_loss > self.max_daily_loss:
                log_risk_management(
                    action="daily_limit_exceeded",
                    reason=f"Potential loss {total_potential_loss:.2f} exceeds daily limit {self.max_daily_loss:.2f}",
                    symbol="",
                    exchange=exchange,
                    current_loss=current_daily_loss,
                    potential_loss=potential_loss
                )
                
                return {
                    "valid": False,
                    "reason": "daily_loss_limit_exceeded",
                    "message": f"Daily loss limit would be exceeded",
                    "current_daily_loss": current_daily_loss,
                    "potential_loss": potential_loss,
                    "daily_limit": self.max_daily_loss
                }
            
            return {
                "valid": True,
                "current_daily_loss": current_daily_loss,
                "remaining_daily_limit": self.max_daily_loss - current_daily_loss
            }
        
        except Exception as e:
            logger.error("Daily loss limit check failed", error=str(e))
            return {
                "valid": False,
                "reason": "check_error",
                "message": str(e)
            }
    
    def record_trade_loss(self, exchange: str, loss_amount: float):
        """Record a trade loss for daily tracking."""
        if loss_amount > 0:  # Only record actual losses
            today = datetime.now().date()
            daily_key = f"{exchange}_{today}"
            
            current_loss = self.daily_losses.get(daily_key, 0.0)
            self.daily_losses[daily_key] = current_loss + loss_amount
            
            logger.info("Trade loss recorded",
                       exchange=exchange,
                       loss_amount=loss_amount,
                       daily_total=self.daily_losses[daily_key])
    
    def calculate_optimal_position_size(self,
                                      account_balance: float,
                                      risk_percentage: float,
                                      entry_price: float,
                                      stop_loss_price: float,
                                      side: str) -> float:
        """Calculate optimal position size based on risk percentage."""
        try:
            # Calculate risk per unit
            if side.lower() == 'buy':
                risk_per_unit = entry_price - stop_loss_price
            else:
                risk_per_unit = stop_loss_price - entry_price
            
            if risk_per_unit <= 0:
                raise ValueError("Invalid stop loss price")
            
            # Calculate maximum risk amount
            max_risk_amount = account_balance * (risk_percentage / 100)
            
            # Calculate optimal position size
            optimal_size = max_risk_amount / risk_per_unit
            
            logger.info("Optimal position size calculated",
                       account_balance=account_balance,
                       risk_percentage=risk_percentage,
                       risk_per_unit=risk_per_unit,
                       optimal_size=optimal_size)
            
            return optimal_size
        
        except Exception as e:
            logger.error("Optimal position size calculation failed", error=str(e))
            raise
    
    def calculate_portfolio_risk(self, 
                               positions: List[Dict[str, Any]],
                               correlations: Optional[Dict[str, float]] = None) -> PortfolioRisk:
        """Calculate portfolio-level risk metrics."""
        try:
            total_exposure = sum(pos.get('value', 0) for pos in positions)
            total_margin_used = sum(pos.get('margin', 0) for pos in positions)
            
            # Calculate portfolio risk (simplified)
            portfolio_risk = sum(pos.get('risk', 0) for pos in positions)
            
            # Calculate concentration risk
            if total_exposure > 0:
                position_weights = [pos.get('value', 0) / total_exposure for pos in positions]
                concentration_risk = max(position_weights) if position_weights else 0
            else:
                concentration_risk = 0
            
            # Calculate correlation risk (simplified)
            correlation_risk = 0.0
            if correlations and len(positions) > 1:
                # This would be a more complex calculation in practice
                avg_correlation = sum(correlations.values()) / len(correlations)
                correlation_risk = avg_correlation * portfolio_risk
            
            return PortfolioRisk(
                total_exposure=total_exposure,
                total_margin_used=total_margin_used,
                available_margin=0.0,  # Would need account info
                portfolio_risk=portfolio_risk,
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk
            )
        
        except Exception as e:
            logger.error("Portfolio risk calculation failed", error=str(e))
            raise
    
    def get_risk_warnings(self, risk_metrics: RiskMetrics) -> List[str]:
        """Get risk warnings based on calculated metrics."""
        warnings = []
        
        if risk_metrics.risk_percentage > 5:
            warnings.append(f"High risk: {risk_metrics.risk_percentage:.1f}% of account at risk")
        
        if risk_metrics.leverage > 10:
            warnings.append(f"High leverage: {risk_metrics.leverage}x")
        
        if risk_metrics.liquidation_price and risk_metrics.position_value > 0:
            warnings.append(f"Liquidation risk at price {risk_metrics.liquidation_price:.2f}")
        
        return warnings
    
    def cleanup_old_daily_losses(self):
        """Clean up old daily loss records (keep last 30 days)."""
        try:
            cutoff_date = datetime.now().date() - timedelta(days=30)
            
            keys_to_remove = []
            for key in self.daily_losses.keys():
                try:
                    date_str = key.split('_', 1)[1]  # Extract date part
                    record_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    if record_date < cutoff_date:
                        keys_to_remove.append(key)
                except:
                    # If we can't parse the date, remove the record
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.daily_losses[key]
            
            if keys_to_remove:
                logger.info("Cleaned up old daily loss records", removed_count=len(keys_to_remove))
        
        except Exception as e:
            logger.error("Failed to cleanup old daily losses", error=str(e))


# Global risk calculator instance
risk_calculator = RiskCalculator()


def get_risk_calculator() -> RiskCalculator:
    """Get the global risk calculator instance."""
    return risk_calculator
