
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: explore_critical_urls.py
# execution: true
from api_server.agent_tools.ask_question_about_documents import ask_question_about_documents

# List of most critical URLs for algorithmic trading project
critical_urls = [
    "https://alertatron.com/docs/automated-trading/command-reference",
    "https://alertatron.com/docs/automated-trading/common-examples", 
    "https://alertatron.com/docs/automated-trading/automated-trading-examples",
    "https://alertatron.com/docs/automated-trading/api-keys-binance-spot",
    "https://alertatron.com/docs/automated-trading/api-keys-binance-futures",
    "https://alertatron.com/docs/automated-trading/api-keys-bybit",
    "https://alertatron.com/docs/getting-alerts-from-tradingview"
]

print("Exploring critical URLs for comprehensive technical documentation...")
print("="*80)

# Extract comprehensive information from command reference - this is crucial for implementation
command_ref_query = """
Extract ALL information about trading commands, syntax, parameters, and examples. 
Include:
1. Complete command syntax and structure
2. All available order types and their parameters
3. Risk management commands (stop loss, take profit, trailing stops)
4. Position management commands
5. All parameter options and their meanings
6. Code examples for each command type
7. Error handling and validation rules
8. Advanced features and special commands
9. Integration with TradingView webhook format
10. Complete reference for algorithmic trading implementation

This information is critical for building a complete algorithmic trading system.
"""

print("1. Extracting complete command reference documentation...")
csv_answer, json_answer, rag_answer, advanced_rag_answers = ask_question_about_documents(
    query=command_ref_query,
    urls=["https://alertatron.com/docs/automated-trading/command-reference"]
)

print("="*80)
print("Command reference extraction completed.")
print("="*80)