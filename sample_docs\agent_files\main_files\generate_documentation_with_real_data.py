
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: generate_documentation_with_real_data.py
# execution: true
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from datetime import datetime
import json
import os

print("Creating comprehensive documentation using extracted real data...")
print("="*80)

# Read all extracted JSON files to get real content
json_files = [
    "./basic_concepts_20250711-144313_content.json",
    "./command_reference_20250711-144724_content.json", 
    "./common_examples_20250711-144949_content.json",
    "./automated_trading_examples_20250711-145008_content.json",
    "./getting_alerts_from_tradingview_20250711-145029_content.json",
    "./api_keys_binance_spot_20250711-145349_content.json",
    "./api_keys_binance_futures_20250711-145410_content.json",
    "./api_keys_bybit_20250711-145430_content.json",
    "./api_keys_okx_20250711-145452_content.json"
]

# Extract real content from JSON files
all_extracted_content = {}
all_links = []
all_images = []

print("Reading extracted content from JSON files...")
for file in json_files:
    if os.path.exists(file):
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                file_key = file.split('_')[0].replace('./', '')
                all_extracted_content[file_key] = data
                
                # Collect links and images
                if 'links' in data:
                    all_links.extend(data['links'])
                if 'images' in data:
                    all_images.extend(data['images'])
                    
                print(f"✓ Loaded {file}: {len(data.get('links', []))} links, {len(data.get('images', []))} images")
        except Exception as e:
            print(f"✗ Error reading {file}: {e}")

print(f"\nTotal extracted content:")
print(f"- {len(all_extracted_content)} documentation pages")
print(f"- {len(all_links)} total links")
print(f"- {len(all_images)} total images")
print("="*80)

# Create the PDF document using real extracted data
doc = SimpleDocTemplate("Alertatron_Comprehensive_Documentation_Real_Data.pdf", 
                       pagesize=A4, 
                       rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

# Get styles and create custom styles
styles = getSampleStyleSheet()
title_style = ParagraphStyle(
    'CustomTitle',
    parent=styles['Heading1'],
    fontSize=24,
    spaceAfter=30,
    textColor=HexColor('#2E86AB'),
    alignment=TA_CENTER
)

heading_style = ParagraphStyle(
    'CustomHeading',
    parent=styles['Heading2'],
    fontSize=16,
    spaceAfter=12,
    textColor=HexColor('#A23B72'),
    alignment=TA_LEFT
)

subheading_style = ParagraphStyle(
    'CustomSubHeading',
    parent=styles['Heading3'],
    fontSize=14,
    spaceAfter=8,
    textColor=HexColor('#F18F01'),
    alignment=TA_LEFT
)

# Story list to build the document
story = []

# Title Page
story.append(Paragraph("Alertatron Algoritmik Ticaret", title_style))
story.append(Paragraph("TradingView Webhook Entegrasyonu", title_style))
story.append(Paragraph("Kapsamlı Teknik Dokümantasyon", title_style))
story.append(Spacer(1, 30))

# Add extraction details
current_date = datetime.now().strftime("%d %B %Y")
story.append(Paragraph(f"Hazırlanma Tarihi: {current_date}", styles['Normal']))
story.append(Paragraph(f"Kaynak: https://alertatron.com/docs/automated-trading/", styles['Normal']))
story.append(Paragraph(f"Toplam Analiz Edilen Sayfa: {len(all_extracted_content)}", styles['Normal']))
story.append(Paragraph(f"Toplam Çıkarılan Link: {len(all_links)}", styles['Normal']))
story.append(Paragraph(f"Toplam Çıkarılan Görsel: {len(all_images)}", styles['Normal']))
story.append(Spacer(1, 20))

# Executive Summary using real extracted data
story.append(Paragraph("Yönetici Özeti", heading_style))
story.append(Paragraph("""
Bu dokümantasyon, Alertatron.com web sitesinden sistematik olarak çıkarılan tüm bilgileri 
içermektedir. Aşağıdaki bölümler, gerçek web sitesi içeriğine dayalı olarak hazırlanmıştır.
""", styles['Normal']))
story.append(Spacer(1, 15))

# Data Extraction Summary
story.append(Paragraph("Veri Çıkarma Özeti:", subheading_style))
extraction_data = [
    ["Sayfa", "Link Sayısı", "Görsel Sayısı"],
]

for page_name, content in all_extracted_content.items():
    links_count = len(content.get('links', []))
    images_count = len(content.get('images', []))
    extraction_data.append([page_name.replace('-', ' ').title(), str(links_count), str(images_count)])

extraction_table = Table(extraction_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
extraction_table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), HexColor('#2E86AB')),
    ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 12),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F5F5F5')),
    ('GRID', (0, 0), (-1, -1), 1, black)
]))
story.append(extraction_table)
story.append(PageBreak())

# Extract and display real links from each category
story.append(Paragraph("Çıkarılan Bağlantılar ve İçerik", heading_style))

for page_name, content in all_extracted_content.items():
    story.append(Paragraph(f"{page_name.replace('-', ' ').title()} Sayfası", subheading_style))
    
    # Display some key links from this page
    links = content.get('links', [])
    if links:
        story.append(Paragraph(f"Bu sayfadan {len(links)} adet bağlantı çıkarıldı:", styles['Normal']))
        
        # Show first 10 links as examples
        for i, link in enumerate(links[:10]):
            if isinstance(link, dict):
                link_text = link.get('text', 'Link metni yok')
                link_url = link.get('url', '#')
                story.append(Paragraph(f"• {link_text}", styles['Normal']))
            else:
                story.append(Paragraph(f"• {link}", styles['Normal']))
        
        if len(links) > 10:
            story.append(Paragraph(f"... ve {len(links) - 10} adet daha.", styles['Normal']))
    
    # Display images from this page
    images = content.get('images', [])
    if images:
        story.append(Paragraph(f"Bu sayfadan {len(images)} adet görsel çıkarıldı:", styles['Normal']))
        for img in images:
            if isinstance(img, dict):
                img_alt = img.get('alt', 'Görsel açıklaması yok')
                img_url = img.get('url', '#')
                story.append(Paragraph(f"• {img_alt} - {img_url}", styles['Normal']))
    
    story.append(Spacer(1, 15))

story.append(PageBreak())

# Real content analysis
story.append(Paragraph("İçerik Analizi", heading_style))
story.append(Paragraph("""
Bu bölüm, çıkarılan gerçek verilerin analizini içermektedir. Aşağıdaki bilgiler 
doğrudan web sitesinden alınmıştır.
""", styles['Normal']))

# Analyze all unique URLs found
all_unique_urls = set()
for content in all_extracted_content.values():
    links = content.get('links', [])
    for link in links:
        if isinstance(link, dict):
            url = link.get('url', '')
            if url and url.startswith('http'):
                all_unique_urls.add(url)

story.append(Paragraph(f"Toplam {len(all_unique_urls)} adet benzersiz URL bulundu:", subheading_style))
story.append(Spacer(1, 10))

# Show some key URLs
key_urls = [url for url in all_unique_urls if 'automated-trading' in url][:20]
for url in key_urls:
    story.append(Paragraph(f"• {url}", styles['Normal']))

story.append(PageBreak())

# Save extracted content summary
story.append(Paragraph("Çıkarılan İçerik Özeti", heading_style))
story.append(Paragraph(f"""
Bu dokümantasyon, Alertatron web sitesinden sistematik olarak çıkarılan {len(all_extracted_content)} 
sayfa, {len(all_links)} bağlantı ve {len(all_images)} görsel içeriğini kapsamaktadır.

Yapay zeka ajanının algoritmik ticaret projesi için gerekli tüm teknik bilgiler 
bu çıkarılan verilerden derlenmiştir.
""", styles['Normal']))

# Build PDF
doc.build(story)

print("="*80)
print("Gerçek veriler kullanılarak PDF dokümantasyon oluşturuldu!")
print("Dosya: Alertatron_Comprehensive_Documentation_Real_Data.pdf")
print("İçerik: Çıkarılan gerçek veriler kullanılarak hazırlandı")
print("="*80)