{"tests/test_exchanges.py::TestExchangeIntegration::test_error_handling": true, "tests/test_orders.py::TestOrderExecutor::test_validate_parameters_success": true, "tests/test_orders.py::TestOrderExecutor::test_validate_parameters_missing_required": true, "tests/test_orders.py::TestOrderExecutor::test_get_current_price": true, "tests/test_orders.py::TestOrderExecutor::test_calculate_price_from_offset_absolute": true, "tests/test_orders.py::TestOrderExecutor::test_calculate_price_from_offset_percentage": true, "tests/test_orders.py::TestOrderExecutor::test_calculate_price_from_offset_entry": true, "tests/test_orders.py::TestOrderExecutor::test_calculate_amount_from_specification_fixed": true, "tests/test_orders.py::TestOrderExecutor::test_calculate_amount_from_specification_percentage": true}