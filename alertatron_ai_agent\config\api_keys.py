"""
API key management for cryptocurrency exchanges.
"""

from typing import Dict, Optional, Any
from pydantic import BaseModel, Field
from cryptography.fernet import Fernet
import os
import json
import base64


class ExchangeCredentials(BaseModel):
    """Exchange API credentials model."""
    
    api_key: str
    secret: str
    passphrase: Optional[str] = None
    sandbox: bool = False
    testnet: bool = False
    
    class Config:
        extra = "allow"


class APIKeyManager:
    """Manages API keys for different exchanges with encryption."""
    
    def __init__(self, encryption_key: Optional[str] = None):
        """Initialize API key manager with optional encryption."""
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode())
        else:
            # Generate a new key if none provided
            key = Fernet.generate_key()
            self.cipher = Fernet(key)
            # Store the key securely (in production, use proper key management)
            os.environ.setdefault("ENCRYPTION_KEY", key.decode())
        
        self._credentials: Dict[str, Dict[str, ExchangeCredentials]] = {}
        self._load_credentials()
    
    def _load_credentials(self):
        """Load credentials from environment or encrypted file."""
        # Try to load from environment variables first
        self._load_from_env()
        
        # Try to load from encrypted file
        credentials_file = os.getenv("CREDENTIALS_FILE", "credentials.enc")
        if os.path.exists(credentials_file):
            self._load_from_file(credentials_file)
    
    def _load_from_env(self):
        """Load credentials from environment variables."""
        exchanges = [
            "binance", "binanceusdm", "binanceus", "bybit", 
            "okx", "bitmex", "bitfinex", "bitget", "deribit"
        ]
        
        for exchange in exchanges:
            # Look for MyKeys pattern in environment
            for key_name in ["MyKeys", "TestKeys", "MainKeys"]:
                api_key = os.getenv(f"{key_name}_{exchange.upper()}_API_KEY")
                secret = os.getenv(f"{key_name}_{exchange.upper()}_SECRET")
                
                if api_key and secret:
                    passphrase = os.getenv(f"{key_name}_{exchange.upper()}_PASSPHRASE")
                    sandbox = os.getenv(f"{key_name}_{exchange.upper()}_SANDBOX", "false").lower() == "true"
                    
                    if exchange not in self._credentials:
                        self._credentials[exchange] = {}
                    
                    self._credentials[exchange][key_name] = ExchangeCredentials(
                        api_key=api_key,
                        secret=secret,
                        passphrase=passphrase,
                        sandbox=sandbox
                    )
    
    def _load_from_file(self, filepath: str):
        """Load encrypted credentials from file."""
        try:
            with open(filepath, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            credentials_data = json.loads(decrypted_data.decode())
            
            for exchange, key_sets in credentials_data.items():
                if exchange not in self._credentials:
                    self._credentials[exchange] = {}
                
                for key_name, creds in key_sets.items():
                    self._credentials[exchange][key_name] = ExchangeCredentials(**creds)
        
        except Exception as e:
            print(f"Warning: Could not load credentials file: {e}")
    
    def save_to_file(self, filepath: str):
        """Save credentials to encrypted file."""
        credentials_data = {}
        
        for exchange, key_sets in self._credentials.items():
            credentials_data[exchange] = {}
            for key_name, creds in key_sets.items():
                credentials_data[exchange][key_name] = creds.dict()
        
        encrypted_data = self.cipher.encrypt(json.dumps(credentials_data).encode())
        
        with open(filepath, 'wb') as f:
            f.write(encrypted_data)
    
    def add_credentials(self, exchange: str, key_name: str, credentials: ExchangeCredentials):
        """Add credentials for an exchange."""
        if exchange not in self._credentials:
            self._credentials[exchange] = {}
        
        self._credentials[exchange][key_name] = credentials
    
    def get_credentials(self, exchange: str, key_name: str) -> Optional[ExchangeCredentials]:
        """Get credentials for a specific exchange and key name."""
        return self._credentials.get(exchange, {}).get(key_name)
    
    def get_all_keys_for_exchange(self, exchange: str) -> Dict[str, ExchangeCredentials]:
        """Get all key sets for an exchange."""
        return self._credentials.get(exchange, {})
    
    def list_available_keys(self) -> Dict[str, list]:
        """List all available key names for each exchange."""
        result = {}
        for exchange, key_sets in self._credentials.items():
            result[exchange] = list(key_sets.keys())
        return result
    
    def validate_credentials(self, exchange: str, key_name: str) -> bool:
        """Validate that credentials exist and have required fields."""
        creds = self.get_credentials(exchange, key_name)
        if not creds:
            return False
        
        # Basic validation
        if not creds.api_key or not creds.secret:
            return False
        
        # Exchange-specific validation
        if exchange in ["okx", "bitget"] and not creds.passphrase:
            return False
        
        return True


# Global API key manager instance
api_key_manager = APIKeyManager()


def get_api_key_manager() -> APIKeyManager:
    """Get the global API key manager instance."""
    return api_key_manager
