["tests/test_exchanges.py::TestBaseExchangeConnector::test_connect", "tests/test_exchanges.py::TestBaseExchangeConnector::test_create_limit_order", "tests/test_exchanges.py::TestBaseExchangeConnector::test_create_market_order", "tests/test_exchanges.py::TestBaseExchangeConnector::test_disconnect", "tests/test_exchanges.py::TestBaseExchangeConnector::test_get_balance", "tests/test_exchanges.py::TestBaseExchangeConnector::test_get_ticker", "tests/test_exchanges.py::TestBaseExchangeConnector::test_test_connection", "tests/test_exchanges.py::TestBinanceConnector::test_exchange_name", "tests/test_exchanges.py::TestBinanceConnector::test_initialize_exchange", "tests/test_exchanges.py::TestExchangeIntegration::test_error_handling", "tests/test_exchanges.py::TestExchangeIntegration::test_full_order_flow", "tests/test_exchanges.py::TestExchangeIntegration::test_order_conversion", "tests/test_exchanges.py::TestExchangeManager::test_exchange_manager_initialization", "tests/test_exchanges.py::TestExchangeManager::test_find_connector_for_key_not_found", "tests/test_exchanges.py::TestExchangeManager::test_get_connection_status", "tests/test_exchanges.py::TestExchangeManager::test_get_connector_not_found", "tests/test_orders.py::TestLimitOrderExecutor::test_execute_limit_order", "tests/test_orders.py::TestLimitOrderExecutor::test_execute_limit_order_missing_offset", "tests/test_orders.py::TestMarketOrderExecutor::test_execute_market_order_missing_amount", "tests/test_orders.py::TestMarketOrderExecutor::test_execute_regular_market_order", "tests/test_orders.py::TestOrderExecutor::test_calculate_amount_from_specification_fixed", "tests/test_orders.py::TestOrderExecutor::test_calculate_amount_from_specification_percentage", "tests/test_orders.py::TestOrderExecutor::test_calculate_price_from_offset_absolute", "tests/test_orders.py::TestOrderExecutor::test_calculate_price_from_offset_entry", "tests/test_orders.py::TestOrderExecutor::test_calculate_price_from_offset_percentage", "tests/test_orders.py::TestOrderExecutor::test_get_current_price", "tests/test_orders.py::TestOrderExecutor::test_validate_parameters_missing_required", "tests/test_orders.py::TestOrderExecutor::test_validate_parameters_success", "tests/test_orders.py::TestOrderFactory::test_create_limit_executor", "tests/test_orders.py::TestOrderFactory::test_create_market_executor", "tests/test_orders.py::TestOrderFactory::test_create_unknown_executor", "tests/test_orders.py::TestOrderFactory::test_get_supported_order_types", "tests/test_orders.py::TestOrderIntegration::test_order_execution_flow", "tests/test_orders.py::TestOrderIntegration::test_order_factory_integration", "tests/test_webhook.py::TestCommandParser::test_get_command_info", "tests/test_webhook.py::TestCommandParser::test_invalid_message_format", "tests/test_webhook.py::TestCommandParser::test_parse_complex_parameters", "tests/test_webhook.py::TestCommandParser::test_parse_limit_order", "tests/test_webhook.py::TestCommandParser::test_parse_market_order", "tests/test_webhook.py::TestWebhookHandler::test_get_processing_stats", "tests/test_webhook.py::TestWebhookHandler::test_parse_webhook_message", "tests/test_webhook.py::TestWebhookHandler::test_process_webhook_invalid_message", "tests/test_webhook.py::TestWebhookHandler::test_test_webhook_processing", "tests/test_webhook.py::TestWebhookIntegration::test_full_webhook_flow", "tests/test_webhook.py::TestWebhookIntegration::test_webhook_validation_flow", "tests/test_webhook.py::TestWebhookValidator::test_get_validation_info", "tests/test_webhook.py::TestWebhookValidator::test_invalid_webhook_format", "tests/test_webhook.py::TestWebhookValidator::test_missing_message", "tests/test_webhook.py::TestWebhookValidator::test_valid_webhook_message"]