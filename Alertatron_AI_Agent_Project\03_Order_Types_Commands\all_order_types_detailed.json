{"market_order": {"name": "Market Order (Piyasa Emri)", "syntax": "market(side, amount, position, reduceOnly)", "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri. Emir borsaya yerleştirilir yerleştirilmez ta<PERSON>mlanır.", "parameters": {"side": {"type": "string", "required": true, "description": "Alım/Satım yönü", "values": ["buy", "sell"], "default": "buy"}, "amount": {"type": "number/string", "required": "position kullanılmıyorsa zorunlu", "description": "İşlem miktarı - çeşitli formatları destekler", "formats": {"absolute": "amount=1000 (1000 kontrat)", "percentage": "amount=50% (bakiye yüzdesi)", "available": "amount=50%x (mevcut bakiye yüzdesi)", "position": "amount=50%p (pozisyon büyüklüğü yüzdesi)", "balance": "amount=50%b (bakiye yüzdesi ile aynı)", "available_alt": "amount=50%a (mevcut bakiye yüzdesi ile aynı)"}}, "position": {"type": "number/string", "required": false, "description": "Hedef pozisyon büyüklüğü - kullanıldığında side ve amount göz ardı edilir", "examples": ["position=1000 (1000 kontrat long pozisyon)", "position=0 (pozis<PERSON><PERSON> kapat)", "position=-500 (500 kontrat short pozisyon)", "position=50% (bakiye yüzdesinde pozisyon)"]}, "reduceOnly": {"type": "boolean", "required": false, "description": "Sadece mevcut pozisyonu azaltmak için kullanılır"}}, "examples": ["market(side='buy', amount=1000)", "market(side='sell', amount='50%')", "market(position=1000)", "market(position=0)  // Pozisyonu kapat"]}, "limit_order": {"name": "Limit Order (Limit Emri)", "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Standart limit emri yer<PERSON>. <PERSON><PERSON> borsaya yerleştirilir yerleştirilmez tamamlanır ve doldurulmasını beklemez.", "parameters": {"side": {"type": "string", "required": true, "description": "<PERSON><PERSON>", "values": ["buy", "sell"]}, "amount": {"type": "number", "required": "position kullanılmıyorsa zorunlu", "default": 0, "description": "İşlem miktarı - market order ile aynı formatları destekler"}, "offset": {"type": "number/string", "required": true, "default": "1%", "description": "<PERSON>yat ofseti - g<PERSON><PERSON><PERSON> fiyat veya ortalama giriş fiyatından", "formats": {"absolute": "offset=50 ($50 ofset)", "percentage": "offset=1% (%1 ofset)", "entry_absolute": "offset=e50 (giriş fiyatından $50)", "entry_percentage": "offset=e1% (giri<PERSON> fiyatından %1)", "absolute_price": "offset=@50000 (mutlak fiyat $50,000)"}}, "postOnly": {"type": "boolean", "required": false, "default": true, "description": "Sadece post emri o<PERSON>"}, "reduceOnly": {"type": "boolean", "required": false, "description": "<PERSON><PERSON><PERSON> poz<PERSON>"}, "position": {"type": "string/number", "required": false, "description": "Pozisyon tabanlı emir boy<PERSON>"}, "tag": {"type": "string", "required": false, "description": "<PERSON><PERSON> etiketi"}}, "examples": ["limit('buy', 1000, '1%', true)", "limit('sell', '50%', 'e1%', true)", "limit('buy', '25%x', '@50000', false)"]}, "stop_order": {"name": "Stop Order (Stop Emri)", "syntax": "stop(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Stop emri - belirli bir fiyat seviyesine ulaşıldığında tetiklenen emir", "parameters": "Limit order ile benzer parametreler", "use_cases": ["Stop-loss emir<PERSON>i", "Breakout strateji<PERSON>i", "Risk yönetimi"]}, "trailing_stop_order": {"name": "Trailing Stop Order (Takip Eden Stop)", "syntax": "trailingStop(side, amount, offset, ...)", "description": "Fiyat lehte hareket ettikçe stop seviyesini otomatik olarak takip eden emir", "benefits": ["<PERSON><PERSON>", "Trend takibi", "Otomatik risk yönetimi"]}, "trailing_take_profit": {"name": "Trailing Take Profit (Takip Eden Kar Al)", "syntax": "trailingTakeProfit(side, amount, offset, ...)", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> fiyat hareketlerine göre otomatik olarak ayarlayan emir"}, "iceberg_order": {"name": "Iceberg Order (Buzdağı Emri)", "syntax": "iceberg(side, amount, sliceAmount, ...)", "description": "Büyük emirleri küçük parçalara bölerek piyasaya sunan algoritmik emir", "purpose": "Büyük emirlerin piyasa et<PERSON> a<PERSON>"}, "grid_order": {"name": "Grid Order (Izgara Emri)", "syntax": "grid(side, amount, gridLevels, ...)", "description": "Belirli fiyat aralıklarında otomatik alım-satım yapan algoritmik emir", "strategy": "Range trading ve volatilite ticareti"}, "twap_order": {"name": "TWAP Order (Zaman Ağırlıklı Ortalama Fiyat)", "syntax": "twap(side, amount, duration, ...)", "description": "Belirli bir zaman diliminde emirleri eşit aralıklarla bölerek icra eden emir", "advantage": "<PERSON><PERSON><PERSON> et<PERSON> etme"}, "ping_pong_order": {"name": "Ping Pong Order", "syntax": "pingPong(side, amount, range, ...)", "description": "Bel<PERSON>li fiyat a<PERSON>ığı<PERSON> alım-satım yaparak kar elde etmeyi hedefleyen emir"}, "market_maker_order": {"name": "Market Maker Order", "syntax": "marketMaker(spread, amount, ...)", "description": "Alış-satış emirlerini eşzamanlı olarak vererek spread'den kar elde etme emri"}, "aggressive_entry_order": {"name": "Aggressive Entry Order", "syntax": "aggressiveEntry(side, amount, ...)", "description": "Pozisyona hızlı giriş için agresif emir stratejisi"}, "dynamic_take_profit": {"name": "Dynamic Take Profit", "syntax": "dynamicTakeProfit(side, amount, levels, ...)", "description": "<PERSON><PERSON><PERSON> k<PERSON>arına göre dinamik olarak ayarlanan kar alma emri"}, "one_cancels_other": {"name": "One Cancels Other (OCO)", "syntax": "oco(order1, order2)", "description": "İki emirden biri gerçekleştiğinde diğerini iptal eden emir ç<PERSON>i"}, "scaled_order": {"name": "Scaled Order (Kademeli Emir)", "syntax": "scaled(side, totalAmount, levels, ...)", "description": "Toplam miktarı farklı fiyat seviyelerine bölen kademeli emir"}, "waiting_limit_order": {"name": "Waiting Limit Order", "syntax": "waitingLimit(side, amount, condition, ...)", "description": "Bel<PERSON><PERSON> ko<PERSON>ullar gerçekleşene kadar bekleyen limit emir"}}