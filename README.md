# Alertatron AI Agent

Professional Algorithmic Trading System with TradingView Integration

## Overview

Alertatron AI Agent is a comprehensive algorithmic trading system that integrates with TradingView webhooks and supports 18 different order types across 9 cryptocurrency exchanges.

## Features

- **TradingView Integration**: Seamless webhook integration with TradingView alerts
- **Multi-Exchange Support**: 9 major cryptocurrency exchanges
- **18 Order Types**: From basic market orders to advanced algorithmic strategies
- **Risk Management**: Built-in risk controls and position management
- **Real-time Processing**: Asynchronous webhook processing
- **Security**: Encrypted API key storage and secure connections

## Supported Exchanges

1. Binance Spot
2. Binance Futures (USDM)
3. Binance US
4. Bybit
5. OKX
6. BitMEX
7. Bitfinex
8. Bitget
9. Deribit

## Supported Order Types

### Basic Orders
- `market()` - Market orders
- `limit()` - Limit orders
- `stop()` - Stop orders
- `stopOrTakeProfit()` - Combined stop-loss/take-profit

### Trailing Orders
- `trailingStop()` - Trailing stop orders
- `trailingTakeProfit()` - Trailing take-profit orders
- `trailingLimit()` - Trailing limit orders

### Algorithmic Orders
- `iceberg()` - Iceberg orders
- `grid()` - Grid trading
- `scaled()` - Scaled orders (DCA)
- `twap()` - Time-weighted average price

### Advanced Strategies
- `pingPong()` - Ping-pong trading
- `marketMaker()` - Market making
- `aggressiveEntry()` - Aggressive entry orders
- `dynamicTakeProfit()` - Dynamic take-profit
- `oco()` - One-cancels-other orders
- `waitingLimit()` - Conditional limit orders
- `managed()` - Fully managed strategies

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd alertatron
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run the application:
```bash
python run.py
```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- **API Keys**: Set up your exchange API keys
- **Database**: Configure PostgreSQL connection
- **Security**: Set encryption keys and secrets
- **Trading**: Configure risk management parameters

### Webhook Format

TradingView alerts should use this format:

```
MyKeys(BTCUSDT) { market(side=buy, amount=50%); } #bot
```

Where:
- `MyKeys` = Your API key set name
- `BTCUSDT` = Trading symbol
- `market(...)` = Order command with parameters
- `#bot` = Bot identifier tag

## API Endpoints

- `GET /` - System information
- `GET /health` - Health check
- `POST /webhook` - Main webhook endpoint
- `POST /webhook/test` - Test webhook validation
- `GET /exchanges` - List supported exchanges
- `GET /orders/types` - List supported order types

## Development Status

### ✅ Phase 1: Core Infrastructure (COMPLETED)
- FastAPI application setup
- Configuration management
- Logging system
- Webhook validation
- Command parsing

### 🚧 Phase 2: Exchange Connectors (IN PROGRESS)
- Base exchange interface
- Individual exchange connectors
- API key management

### 📋 Phase 3: Order Types (PLANNED)
- Basic order implementations
- Trailing order logic
- Algorithmic order strategies
- Advanced trading strategies

### 📋 Phase 4: Risk Management (PLANNED)
- Position size limits
- Daily loss limits
- Risk calculation engine

## Testing

Run tests with:
```bash
pytest tests/
```

## Security

- API keys are encrypted at rest
- Secure webhook validation
- Rate limiting and error handling
- Comprehensive logging for audit trails

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Disclaimer

This software is for educational and research purposes. Trading cryptocurrencies involves substantial risk. Use at your own risk and never trade with money you cannot afford to lose.

## Support

For support and questions, please open an issue on GitHub.
