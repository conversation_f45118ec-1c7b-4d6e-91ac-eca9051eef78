{"market_order": {"name": "Market Order (Piyasa Emri)", "syntax": "market(side, amount, position, reduceOnly)", "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri", "parameters": {"side": {"type": "string", "required": true, "values": ["buy", "sell"], "default": "buy"}, "amount": {"type": "mixed", "formats": ["1000", "50%", "50%x", "50%p", "50%b", "50%a"]}, "position": {"type": "mixed", "description": "<PERSON><PERSON><PERSON> p<PERSON> büyüklüğü", "examples": ["position=1000", "position=0", "position=-500"]}, "reduceOnly": {"type": "boolean", "description": "<PERSON><PERSON>e mevcut pozisyonu azaltmak için"}}, "examples": ["market(side='buy', amount=1000)", "market(position=0)", "market(side='sell', amount='50%')"]}, "limit_order": {"name": "Limit Order (Limit Emri)", "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Belirli fiyattan limit <PERSON><PERSON>", "parameters": {"side": {"type": "string", "required": true, "values": ["buy", "sell"]}, "amount": {"type": "number", "required": "position kullanılmıyorsa zorunlu"}, "offset": {"type": "string", "required": true, "default": "1%", "formats": ["50", "1%", "e50", "e1%", "@50000"]}, "postOnly": {"type": "boolean", "default": true, "description": "Sadece post emri"}, "reduceOnly": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> poz<PERSON>"}, "position": {"type": "mixed", "description": "Pozisyon tabanlı boyutlandırma"}, "tag": {"type": "string", "description": "<PERSON><PERSON> etiketi"}}, "examples": ["limit('buy', 1000, '1%', true)", "limit('sell', '50%', 'e1%', true)"]}, "stop_order": {"name": "Stop Order (Stop Emri)", "syntax": "stop(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Belirli fiyat seviyesine ulaşıldığında tetiklenen emir", "use_cases": ["Stop-loss emir<PERSON>i", "Breakout strateji<PERSON>i", "Risk yönetimi"], "parameters": "Limit order ile benzer parametreler kullanır"}, "stop_or_take_profit_order": {"name": "Stop or Take Profit Order", "syntax": "stopOrTakeProfit(side, amount, stopPrice, takeProfitPrice, ...)", "description": "Stop-loss ve take-profit seviyelerini birlikte belirleyen emir", "use_cases": ["Otomatik risk yönetimi", "<PERSON><PERSON>", "<PERSON><PERSON>p sı<PERSON>"]}, "trailing_stop_order": {"name": "Trailing Stop Order (Takip Eden Stop)", "syntax": "trailingStop(side, amount, offset, ...)", "description": "Fiyat lehte hareket ettikçe stop seviyesini otomatik takip eden emir", "benefits": ["<PERSON><PERSON>", "Trend takibi", "Otomatik risk yönetimi"]}, "trailing_take_profit": {"name": "Trailing Take Profit (Takip Eden Kar Al)", "syntax": "trailingTakeProfit(side, amount, offset, ...)", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> fiyat hareketlerine göre otomatik ayarlayan emir", "benefits": ["<PERSON><PERSON><PERSON><PERSON> kar yakalama", "Trend devamında pozisyon tutma"]}, "trailing_limit_order": {"name": "Trailing Limit Order", "syntax": "trailingLimit(side, amount, offset, ...)", "description": "<PERSON><PERSON><PERSON> ha<PERSON>ni takip eden limit emir"}, "iceberg_order": {"name": "Iceberg Order (Buzdağı Emri)", "syntax": "iceberg(side, amount, sliceAmount, ...)", "description": "Büyük emirleri küçük parçalara bölerek piyasa etkisini azaltan emir", "purpose": "Büyük emirlerin piyasa etkisini minimize etme", "parameters": {"sliceAmount": "Her seferinde gösterilen emir miktarı"}}, "grid_order": {"name": "Grid Order (Izgara Emri)", "syntax": "grid(side, amount, gridLevels, priceRange, ...)", "description": "Belirli fiyat aralıklarında otomatik alım-satım yapan emir", "strategy": "Range trading ve volatilite ticareti", "parameters": {"gridLevels": "Izgara seviye sayısı", "priceRange": "Fiyat a<PERSON>ığı"}}, "scaled_order": {"name": "Scaled Order (Kademeli Emir)", "syntax": "scaled(side, totalAmount, levels, priceRange, ...)", "description": "Toplam miktarı farklı fiyat seviyelerine bölen kademeli emir", "use_cases": ["DCA stratejisi", "Ortalama maliyet düşürme"]}, "twap_order": {"name": "TWAP Order (Zaman Ağırlıklı Ortalama Fiyat)", "syntax": "twap(side, amount, duration, intervals, ...)", "description": "Belirli zaman diliminde emirleri eşit aralıklarla bölen emir", "advantage": "<PERSON><PERSON><PERSON> et<PERSON> etme", "parameters": {"duration": "<PERSON><PERSON> süre", "intervals": "<PERSON><PERSON>"}}, "ping_pong_order": {"name": "Ping Pong Order", "syntax": "pingPong(side, amount, range, profitMargin, ...)", "description": "Belirli fiyat aralığında sürekli alım-satım yaparak kar elde etme", "strategy": "Sideways market'lerde kar elde etme"}, "market_maker_order": {"name": "Market Maker Order", "syntax": "marketMaker(spread, amount, ...)", "description": "Alış-satış emirlerini eşzamanlı vererek spread'den kar elde etme", "strategy": "Likidite sağlama ve spread arbitrajı"}, "aggressive_entry_order": {"name": "Aggressive Entry Order", "syntax": "aggressiveEntry(side, amount, slippage, ...)", "description": "Pozisyona hızlı giriş için agresif emir stratejisi", "use_cases": ["Breakout ticareti", "<PERSON><PERSON> yakalama"]}, "dynamic_take_profit": {"name": "Dynamic Take Profit", "syntax": "dynamicTakeProfit(side, amount, levels, conditions, ...)", "description": "<PERSON><PERSON><PERSON> k<PERSON>arına göre dinamik kar alma seviyesi", "parameters": {"levels": "Farklı kar alma seviyeler", "conditions": "Tetikleme koşulları"}}, "one_cancels_other": {"name": "One Cancels Other (OCO)", "syntax": "oco(order1, order2)", "description": "İki emirden biri gerçekleştiğinde diğerini iptal eden emir ç<PERSON>i", "use_cases": ["Stop-loss ve take-profit kombinasyonu", "<PERSON><PERSON> yönlü strateji"]}, "waiting_limit_order": {"name": "Waiting Limit Order", "syntax": "waitingLimit(side, amount, condition, timeout, ...)", "description": "Bel<PERSON><PERSON> ko<PERSON>ullar gerçekleşene kadar bekleyen limit emir", "parameters": {"condition": "Bekleme koşulu", "timeout": "<PERSON><PERSON><PERSON><PERSON> bekle<PERSON>i"}}, "managed_order": {"name": "Managed Order", "syntax": "managed(strategy, parameters, ...)", "description": "Otomatik yönetilen algoritmik emir stratejisi"}}