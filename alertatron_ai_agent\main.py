"""
Main application entry point for Alertatron AI Agent.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
from typing import Dict, Any
import asyncio

from .config.settings import get_settings
from .utils.logger import get_logger, log_error
from .webhook.handler import <PERSON>hook<PERSON>andler
from .webhook.validator import WebhookValidator
from .exchanges.exchange_manager import get_exchange_manager
from .risk_management.position_manager import get_position_manager
from .risk_management.risk_calculator import get_risk_calculator


# Initialize logger
logger = get_logger("main")
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting Alertatron AI Agent", version=settings.app_version)
    
    # Initialize components
    app.state.webhook_handler = WebhookHandler()
    app.state.webhook_validator = WebhookValidator()
    app.state.exchange_manager = get_exchange_manager()
    
    logger.info("Application startup complete")
    yield
    
    logger.info("Shutting down Alertatron AI Agent")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Professional Algorithmic Trading System with TradingView Integration",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint with system information."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "supported_exchanges": settings.supported_exchanges,
        "max_concurrent_orders": settings.max_concurrent_orders
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",  # Will be replaced with actual timestamp
        "version": settings.app_version
    }


@app.post("/webhook")
async def handle_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    webhook_data: Dict[str, Any] = None
):
    """
    Main webhook endpoint for TradingView signals.
    
    Expected format: MyKeys(SYMBOL) { command(parameters); } #bot
    """
    try:
        # Get webhook data from request
        if webhook_data is None:
            webhook_data = await request.json()
        
        # Validate webhook
        is_valid, validation_error = app.state.webhook_validator.validate_webhook(webhook_data)
        if not is_valid:
            logger.error("Webhook validation failed", error=validation_error)
            raise HTTPException(status_code=400, detail=validation_error)
        
        # Process webhook in background
        background_tasks.add_task(
            app.state.webhook_handler.process_webhook,
            webhook_data
        )
        
        return {
            "status": "received",
            "message": "Webhook processed successfully",
            "timestamp": "2024-01-01T00:00:00Z"  # Will be replaced with actual timestamp
        }
    
    except Exception as e:
        log_error(
            component="webhook_endpoint",
            error_type=type(e).__name__,
            error_message=str(e)
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/webhook/test")
async def test_webhook(webhook_data: Dict[str, Any]):
    """Test endpoint for webhook validation without execution."""
    try:
        is_valid, validation_error = app.state.webhook_validator.validate_webhook(webhook_data)
        
        if not is_valid:
            return {
                "valid": False,
                "error": validation_error,
                "status": "validation_failed"
            }
        
        # Parse the webhook to show what would be executed
        parsed_data = app.state.webhook_handler.parse_webhook_message(webhook_data)
        
        return {
            "valid": True,
            "parsed_data": parsed_data,
            "status": "validation_passed",
            "note": "This is a test - no actual trades were executed"
        }
    
    except Exception as e:
        return {
            "valid": False,
            "error": str(e),
            "status": "error"
        }


@app.get("/exchanges")
async def list_exchanges():
    """List supported exchanges and their status."""
    try:
        connection_status = app.state.exchange_manager.get_connection_status()
        return {
            "supported_exchanges": settings.supported_exchanges,
            "connection_status": connection_status,
            "status": "Exchange connectors active"
        }
    except Exception as e:
        return {
            "supported_exchanges": settings.supported_exchanges,
            "error": str(e),
            "status": "Error getting exchange status"
        }


@app.get("/exchanges/test")
async def test_exchanges():
    """Test all exchange connections."""
    try:
        test_results = await app.state.exchange_manager.test_all_connections()
        return {
            "status": "completed",
            "test_results": test_results
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.get("/exchanges/balances")
async def get_all_balances():
    """Get balances from all connected exchanges."""
    try:
        balances = await app.state.exchange_manager.get_all_balances()
        return {
            "status": "success",
            "balances": balances
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.get("/exchanges/positions")
async def get_all_positions():
    """Get positions from all connected exchanges."""
    try:
        positions = await app.state.exchange_manager.get_all_positions()
        return {
            "status": "success",
            "positions": positions
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.get("/orders/types")
async def list_order_types():
    """List all supported order types."""
    from .orders.order_factory import get_order_factory

    order_factory = get_order_factory()
    supported_types = order_factory.get_supported_order_types()

    all_types = [
        "market", "limit", "stop", "stopOrTakeProfit",
        "trailingStop", "trailingTakeProfit", "trailingLimit",
        "iceberg", "grid", "scaled", "twap",
        "pingPong", "marketMaker", "aggressiveEntry",
        "dynamicTakeProfit", "oco", "waitingLimit", "managed"
    ]

    return {
        "total_order_types": len(all_types),
        "implemented_order_types": len(supported_types),
        "supported_types": supported_types,
        "all_planned_types": all_types,
        "status": f"{len(supported_types)}/{len(all_types)} order types implemented"
    }


@app.get("/risk/summary")
async def get_risk_summary():
    """Get risk management summary."""
    try:
        position_manager = get_position_manager()
        risk_calculator = get_risk_calculator()

        position_summary = position_manager.get_position_summary()

        return {
            "status": "success",
            "position_summary": position_summary,
            "risk_limits": {
                "max_position_size": risk_calculator.max_position_size,
                "max_daily_loss": risk_calculator.max_daily_loss
            },
            "daily_losses": risk_calculator.daily_losses
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.post("/risk/emergency-close/{exchange}")
async def emergency_close_positions(exchange: str, reason: str = "manual"):
    """Emergency close all positions on an exchange."""
    try:
        position_manager = get_position_manager()
        exchange_manager = app.state.exchange_manager

        # Find a connector for the exchange
        connector = None
        for key_name in exchange_manager.connections.get(exchange, {}):
            connector = exchange_manager.get_connector(exchange, key_name)
            if connector:
                break

        if not connector:
            return {
                "status": "error",
                "message": f"No active connector found for exchange: {exchange}"
            }

        await position_manager.emergency_close_all(connector, reason)

        return {
            "status": "success",
            "message": f"Emergency close initiated for {exchange}",
            "reason": reason
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    log_error(
        component="global_handler",
        error_type=type(exc).__name__,
        error_message=str(exc),
        path=str(request.url)
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


def main():
    """Main function to run the application."""
    uvicorn.run(
        "alertatron_ai_agent.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
