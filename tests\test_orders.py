"""
Tests for order execution functionality.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from datetime import datetime

from alertatron_ai_agent.orders.order_factory import OrderFactory, OrderExecutor
from alertatron_ai_agent.orders.basic_orders import MarketOrderExecutor, LimitOrderExecutor
from alertatron_ai_agent.webhook.parser import ParsedCommand


class MockConnector:
    """Mock exchange connector for testing."""
    
    def __init__(self):
        self.exchange_name = "test_exchange"
        self.create_market_order = AsyncMock()
        self.create_limit_order = AsyncMock()
        self.create_stop_order = AsyncMock()
        self.get_ticker = AsyncMock()
        self.get_balance = AsyncMock()
        self.get_positions = AsyncMock()
    
    async def get_current_price(self, symbol):
        """Mock get current price."""
        return 50000.0


class TestOrderFactory:
    """Test order factory functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.factory = OrderFactory()
        self.mock_connector = MockConnector()
    
    def test_create_market_executor(self):
        """Test creating market order executor."""
        executor = self.factory.create_executor("market", self.mock_connector)
        assert executor is not None
        assert isinstance(executor, MarketOrderExecutor)
    
    def test_create_limit_executor(self):
        """Test creating limit order executor."""
        executor = self.factory.create_executor("limit", self.mock_connector)
        assert executor is not None
        assert isinstance(executor, LimitOrderExecutor)
    
    def test_create_unknown_executor(self):
        """Test creating unknown order executor."""
        executor = self.factory.create_executor("unknown", self.mock_connector)
        assert executor is None
    
    def test_get_supported_order_types(self):
        """Test getting supported order types."""
        types = self.factory.get_supported_order_types()
        assert isinstance(types, list)
        assert "market" in types
        assert "limit" in types


class TestOrderExecutor:
    """Test base order executor functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_connector = MockConnector()
        self.executor = OrderExecutor(self.mock_connector)
    
    def test_validate_parameters_success(self):
        """Test successful parameter validation."""
        params = {"side": "buy", "amount": 100}
        required = ["side", "amount"]
        optional = ["price"]
        
        validated = self.executor.validate_parameters(params, required, optional)
        assert validated["side"] == "buy"
        assert validated["amount"] == 100
    
    def test_validate_parameters_missing_required(self):
        """Test parameter validation with missing required parameter."""
        params = {"side": "buy"}
        required = ["side", "amount"]
        
        with pytest.raises(ValueError, match="Required parameter"):
            self.executor.validate_parameters(params, required)
    
    @pytest.mark.asyncio
    async def test_get_current_price(self):
        """Test getting current price."""
        self.mock_connector.get_ticker.return_value = {"last": 50000.0}
        
        price = await self.executor.get_current_price("BTCUSDT")
        assert price == 50000.0
    
    def test_calculate_price_from_offset_absolute(self):
        """Test calculating price from absolute offset."""
        current_price = 50000.0
        offset = "@49000"
        side = "buy"
        
        calculated_price = self.executor.calculate_price_from_offset(current_price, offset, side)
        assert calculated_price == 49000.0
    
    def test_calculate_price_from_offset_percentage(self):
        """Test calculating price from percentage offset."""
        current_price = 50000.0
        offset = "1%"
        side = "buy"
        
        calculated_price = self.executor.calculate_price_from_offset(current_price, offset, side)
        assert calculated_price == 49500.0  # 50000 * (1 - 0.01)
    
    def test_calculate_price_from_offset_entry(self):
        """Test calculating price from entry offset."""
        current_price = 50000.0
        offset = "e1%"
        side = "sell"
        
        calculated_price = self.executor.calculate_price_from_offset(current_price, offset, side)
        assert calculated_price == 50500.0  # 50000 * (1 + 0.01)
    
    def test_calculate_amount_from_specification_fixed(self):
        """Test calculating amount from fixed specification."""
        amount_spec = 100
        symbol = "BTCUSDT"
        current_price = 50000.0
        
        calculated_amount = self.executor.calculate_amount_from_specification(amount_spec, symbol, current_price)
        assert calculated_amount == 100.0
    
    def test_calculate_amount_from_specification_percentage(self):
        """Test calculating amount from percentage specification."""
        amount_spec = "50%"
        symbol = "BTCUSDT"
        current_price = 50000.0
        
        with pytest.raises(ValueError, match="Percentage-based amounts not yet implemented"):
            self.executor.calculate_amount_from_specification(amount_spec, symbol, current_price)


class TestMarketOrderExecutor:
    """Test market order executor."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_connector = MockConnector()
        self.executor = MarketOrderExecutor(self.mock_connector)
    
    @pytest.mark.asyncio
    async def test_execute_regular_market_order(self):
        """Test executing regular market order."""
        # Setup mocks
        self.mock_connector.get_ticker.return_value = {"last": 50000.0}
        self.mock_connector.create_market_order.return_value = Mock(
            id="test_order_123",
            symbol="BTCUSDT",
            side="buy",
            amount=0.001,
            status="filled"
        )
        
        # Create parsed command
        parsed_command = ParsedCommand(
            key_name="TestKeys",
            symbol="BTCUSDT",
            command="market",
            parameters={"side": "buy", "amount": 0.001},
            bot_tag="bot",
            raw_message="TestKeys(BTCUSDT) { market(side=buy, amount=0.001); } #bot"
        )
        
        # Execute order
        result = await self.executor.execute(parsed_command)
        
        # Verify result
        assert result["status"] == "success"
        assert result["order_type"] == "market"
        assert "order" in result
        assert "execution_price" in result
    
    @pytest.mark.asyncio
    async def test_execute_market_order_missing_amount(self):
        """Test executing market order with missing amount."""
        parsed_command = ParsedCommand(
            key_name="TestKeys",
            symbol="BTCUSDT",
            command="market",
            parameters={"side": "buy"},
            bot_tag="bot",
            raw_message="TestKeys(BTCUSDT) { market(side=buy); } #bot"
        )
        
        with pytest.raises(ValueError, match="Amount is required"):
            await self.executor.execute(parsed_command)


class TestLimitOrderExecutor:
    """Test limit order executor."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_connector = MockConnector()
        self.executor = LimitOrderExecutor(self.mock_connector)
    
    @pytest.mark.asyncio
    async def test_execute_limit_order(self):
        """Test executing limit order."""
        # Setup mocks
        self.mock_connector.get_ticker.return_value = {"last": 50000.0}
        self.mock_connector.create_limit_order.return_value = Mock(
            id="test_limit_123",
            symbol="BTCUSDT",
            side="buy",
            amount=0.001,
            price=49500.0,
            status="open"
        )
        
        # Create parsed command
        parsed_command = ParsedCommand(
            key_name="TestKeys",
            symbol="BTCUSDT",
            command="limit",
            parameters={"side": "buy", "amount": 0.001, "offset": "1%"},
            bot_tag="bot",
            raw_message="TestKeys(BTCUSDT) { limit(side=buy, amount=0.001, offset=1%); } #bot"
        )
        
        # Execute order
        result = await self.executor.execute(parsed_command)
        
        # Verify result
        assert result["status"] == "success"
        assert result["order_type"] == "limit"
        assert "limit_price" in result
        assert "current_price" in result
    
    @pytest.mark.asyncio
    async def test_execute_limit_order_missing_offset(self):
        """Test executing limit order with missing offset."""
        parsed_command = ParsedCommand(
            key_name="TestKeys",
            symbol="BTCUSDT",
            command="limit",
            parameters={"side": "buy", "amount": 0.001},
            bot_tag="bot",
            raw_message="TestKeys(BTCUSDT) { limit(side=buy, amount=0.001); } #bot"
        )
        
        with pytest.raises(ValueError, match="offset"):
            await self.executor.execute(parsed_command)


@pytest.fixture
def sample_parsed_command():
    """Sample parsed command for testing."""
    return ParsedCommand(
        key_name="TestKeys",
        symbol="BTCUSDT",
        command="market",
        parameters={"side": "buy", "amount": 0.001},
        bot_tag="bot",
        raw_message="TestKeys(BTCUSDT) { market(side=buy, amount=0.001); } #bot"
    )


class TestOrderIntegration:
    """Integration tests for order functionality."""
    
    def test_order_factory_integration(self):
        """Test order factory integration."""
        factory = OrderFactory()
        mock_connector = MockConnector()
        
        # Test creating different order types
        market_executor = factory.create_executor("market", mock_connector)
        limit_executor = factory.create_executor("limit", mock_connector)
        
        assert market_executor is not None
        assert limit_executor is not None
        assert market_executor != limit_executor
    
    @pytest.mark.asyncio
    async def test_order_execution_flow(self, sample_parsed_command):
        """Test complete order execution flow."""
        factory = OrderFactory()
        mock_connector = MockConnector()
        
        # Setup mock responses
        mock_connector.get_ticker.return_value = {"last": 50000.0}
        mock_connector.create_market_order.return_value = Mock(
            id="test_123",
            symbol="BTCUSDT",
            side="buy",
            amount=0.001,
            status="filled"
        )
        
        # Create and execute order
        executor = factory.create_executor(sample_parsed_command.command, mock_connector)
        assert executor is not None
        
        result = await executor.execute(sample_parsed_command)
        assert result["status"] == "success"
