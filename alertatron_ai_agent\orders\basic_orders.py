"""
Basic order type implementations: market, limit, stop, stopOrTakeProfit.
"""

from typing import Dict, Any
import asyncio

from .order_factory import OrderExecutor
from ..webhook.parser import ParsedCommand
from ..exchanges.base_connector import OrderSide
from ..utils.logger import log_trade_execution


class MarketOrderExecutor(OrderExecutor):
    """Executor for market orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a market order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=[],  # side and amount can have defaults
                optional=['side', 'amount', 'position', 'reduceOnly']
            )
            
            # Handle different parameter combinations
            if 'position' in params:
                # Position-based order
                return await self._execute_position_based_order(parsed_command, params)
            else:
                # Regular market order
                return await self._execute_regular_market_order(parsed_command, params)
        
        except Exception as e:
            self.logger.error("Market order execution failed", error=str(e))
            raise
    
    async def _execute_regular_market_order(self, parsed_command: ParsedCommand, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a regular market order with side and amount."""
        side = params.get('side', 'buy')
        amount = params.get('amount')
        
        if not amount:
            raise ValueError("Amount is required for market orders")
        
        # Calculate actual amount
        current_price = await self.get_current_price(parsed_command.symbol)
        actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
        
        # Create order
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
        
        order = await self.connector.create_market_order(
            symbol=parsed_command.symbol,
            side=order_side,
            amount=actual_amount,
            params={'reduceOnly': params.get('reduceOnly', False)}
        )
        
        # Log trade execution
        log_trade_execution(
            exchange=self.connector.exchange_name,
            symbol=parsed_command.symbol,
            order_type='market',
            side=side,
            amount=actual_amount,
            order_id=order.id,
            status='executed'
        )
        
        return {
            "status": "success",
            "order_type": "market",
            "order": order.dict(),
            "execution_price": current_price,
            "message": "Market order executed successfully"
        }
    
    async def _execute_position_based_order(self, parsed_command: ParsedCommand, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a position-based market order."""
        target_position = params.get('position', 0)
        
        # Get current positions
        positions = await self.connector.get_positions()
        current_position = 0
        
        for pos in positions:
            if pos.symbol == parsed_command.symbol:
                current_position = pos.size if pos.side == 'long' else -pos.size
                break
        
        # Calculate required order
        position_diff = target_position - current_position
        
        if position_diff == 0:
            return {
                "status": "success",
                "order_type": "market",
                "message": "Already at target position",
                "current_position": current_position,
                "target_position": target_position
            }
        
        # Determine side and amount
        side = 'buy' if position_diff > 0 else 'sell'
        amount = abs(position_diff)
        
        order_side = OrderSide.BUY if side == 'buy' else OrderSide.SELL
        
        order = await self.connector.create_market_order(
            symbol=parsed_command.symbol,
            side=order_side,
            amount=amount
        )
        
        # Log trade execution
        log_trade_execution(
            exchange=self.connector.exchange_name,
            symbol=parsed_command.symbol,
            order_type='market_position',
            side=side,
            amount=amount,
            order_id=order.id,
            status='executed',
            current_position=current_position,
            target_position=target_position
        )
        
        return {
            "status": "success",
            "order_type": "market",
            "order": order.dict(),
            "current_position": current_position,
            "target_position": target_position,
            "position_change": position_diff,
            "message": "Position-based market order executed successfully"
        }


class LimitOrderExecutor(OrderExecutor):
    """Executor for limit orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a limit order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'offset'],
                optional=['amount', 'postOnly', 'reduceOnly', 'position', 'tag']
            )
            
            side = params['side']
            offset = params['offset']
            amount = params.get('amount')
            
            if not amount:
                raise ValueError("Amount is required for limit orders")
            
            # Get current price and calculate limit price
            current_price = await self.get_current_price(parsed_command.symbol)
            limit_price = self.calculate_price_from_offset(current_price, offset, side)
            
            # Calculate actual amount
            actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
            
            # Create limit order
            order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
            
            order_params = {}
            if params.get('postOnly'):
                order_params['postOnly'] = True
            if params.get('reduceOnly'):
                order_params['reduceOnly'] = True
            if params.get('tag'):
                order_params['clientOrderId'] = params['tag']
            
            order = await self.connector.create_limit_order(
                symbol=parsed_command.symbol,
                side=order_side,
                amount=actual_amount,
                price=limit_price,
                params=order_params
            )
            
            # Log trade execution
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='limit',
                side=side,
                amount=actual_amount,
                price=limit_price,
                order_id=order.id,
                status='placed'
            )
            
            return {
                "status": "success",
                "order_type": "limit",
                "order": order.dict(),
                "limit_price": limit_price,
                "current_price": current_price,
                "offset": offset,
                "message": "Limit order placed successfully"
            }
        
        except Exception as e:
            self.logger.error("Limit order execution failed", error=str(e))
            raise


class StopOrderExecutor(OrderExecutor):
    """Executor for stop orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a stop order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'offset'],
                optional=['amount', 'postOnly', 'reduceOnly', 'position', 'tag']
            )
            
            side = params['side']
            offset = params['offset']
            amount = params.get('amount')
            
            if not amount:
                raise ValueError("Amount is required for stop orders")
            
            # Get current price and calculate stop price
            current_price = await self.get_current_price(parsed_command.symbol)
            stop_price = self.calculate_price_from_offset(current_price, offset, side)
            
            # Calculate actual amount
            actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
            
            # Create stop order
            order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
            
            order = await self.connector.create_stop_order(
                symbol=parsed_command.symbol,
                side=order_side,
                amount=actual_amount,
                stop_price=stop_price
            )
            
            # Log trade execution
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='stop',
                side=side,
                amount=actual_amount,
                price=stop_price,
                order_id=order.id,
                status='placed'
            )
            
            return {
                "status": "success",
                "order_type": "stop",
                "order": order.dict(),
                "stop_price": stop_price,
                "current_price": current_price,
                "offset": offset,
                "message": "Stop order placed successfully"
            }
        
        except Exception as e:
            self.logger.error("Stop order execution failed", error=str(e))
            raise


class StopOrTakeProfitExecutor(OrderExecutor):
    """Executor for combined stop-loss and take-profit orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a stop-loss or take-profit order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side'],
                optional=['amount', 'stopPrice', 'takeProfitPrice', 'stopOffset', 'takeProfitOffset']
            )
            
            side = params['side']
            amount = params.get('amount')
            
            if not amount:
                raise ValueError("Amount is required for stopOrTakeProfit orders")
            
            # Get current price
            current_price = await self.get_current_price(parsed_command.symbol)
            
            # Calculate actual amount
            actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
            
            orders_created = []
            
            # Create stop-loss order if specified
            if 'stopPrice' in params or 'stopOffset' in params:
                stop_price = params.get('stopPrice')
                if not stop_price and 'stopOffset' in params:
                    stop_price = self.calculate_price_from_offset(current_price, params['stopOffset'], side)
                
                if stop_price:
                    stop_order = await self.connector.create_stop_order(
                        symbol=parsed_command.symbol,
                        side=OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL,
                        amount=actual_amount,
                        stop_price=float(stop_price)
                    )
                    orders_created.append(("stop_loss", stop_order))
            
            # Create take-profit order if specified
            if 'takeProfitPrice' in params or 'takeProfitOffset' in params:
                tp_price = params.get('takeProfitPrice')
                if not tp_price and 'takeProfitOffset' in params:
                    # For take profit, reverse the side logic
                    tp_side = 'sell' if side.lower() == 'buy' else 'buy'
                    tp_price = self.calculate_price_from_offset(current_price, params['takeProfitOffset'], tp_side)
                
                if tp_price:
                    tp_order = await self.connector.create_limit_order(
                        symbol=parsed_command.symbol,
                        side=OrderSide.SELL if side.lower() == 'buy' else OrderSide.BUY,
                        amount=actual_amount,
                        price=float(tp_price)
                    )
                    orders_created.append(("take_profit", tp_order))
            
            # Log trade execution
            for order_type, order in orders_created:
                log_trade_execution(
                    exchange=self.connector.exchange_name,
                    symbol=parsed_command.symbol,
                    order_type=f'stopOrTakeProfit_{order_type}',
                    side=side,
                    amount=actual_amount,
                    price=order.price,
                    order_id=order.id,
                    status='placed'
                )
            
            return {
                "status": "success",
                "order_type": "stopOrTakeProfit",
                "orders_created": len(orders_created),
                "orders": [{"type": ot, "order": o.dict()} for ot, o in orders_created],
                "current_price": current_price,
                "message": f"Created {len(orders_created)} orders successfully"
            }
        
        except Exception as e:
            self.logger.error("StopOrTakeProfit order execution failed", error=str(e))
            raise
