"""
Position management and tracking system.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel

from ..exchanges.base_connector import BaseExchangeConnector, Position
from ..utils.logger import get_logger, log_risk_management
from .risk_calculator import get_risk_calculator, RiskMetrics


logger = get_logger("position_manager")


class ManagedPosition(BaseModel):
    """Enhanced position model with risk management."""
    
    symbol: str
    exchange: str
    side: str
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    risk_metrics: Optional[RiskMetrics] = None
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None
    max_loss_hit: bool = False
    created_at: datetime
    updated_at: datetime


class PositionManager:
    """Manages trading positions with risk controls."""
    
    def __init__(self):
        self.risk_calculator = get_risk_calculator()
        self.managed_positions: Dict[str, ManagedPosition] = {}
        self.position_limits = {
            'max_positions_per_symbol': 3,
            'max_total_positions': 20,
            'max_correlation_exposure': 0.3
        }
    
    async def add_position(self,
                          connector: BaseExchangeConnector,
                          symbol: str,
                          side: str,
                          size: float,
                          entry_price: float,
                          stop_loss_price: Optional[float] = None,
                          take_profit_price: Optional[float] = None) -> str:
        """Add a new position to management."""
        try:
            position_id = f"{connector.exchange_name}_{symbol}_{datetime.now().timestamp()}"
            
            # Calculate risk metrics
            account_balance = await self._get_account_balance(connector)
            risk_metrics = self.risk_calculator.calculate_position_risk(
                symbol=symbol,
                side=side,
                amount=size,
                entry_price=entry_price,
                stop_loss_price=stop_loss_price,
                account_balance=account_balance
            )
            
            # Create managed position
            managed_position = ManagedPosition(
                symbol=symbol,
                exchange=connector.exchange_name,
                side=side,
                size=size,
                entry_price=entry_price,
                current_price=entry_price,
                unrealized_pnl=0.0,
                risk_metrics=risk_metrics,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.managed_positions[position_id] = managed_position
            
            logger.info("Position added to management",
                       position_id=position_id,
                       symbol=symbol,
                       exchange=connector.exchange_name,
                       risk_percentage=risk_metrics.risk_percentage)
            
            return position_id
        
        except Exception as e:
            logger.error("Failed to add position to management", error=str(e))
            raise
    
    async def update_positions(self, connector: BaseExchangeConnector):
        """Update all positions for a specific exchange."""
        try:
            # Get current positions from exchange
            current_positions = await connector.get_positions()
            
            # Update managed positions
            for position_id, managed_pos in self.managed_positions.items():
                if managed_pos.exchange == connector.exchange_name:
                    # Find matching position
                    matching_pos = None
                    for pos in current_positions:
                        if pos.symbol == managed_pos.symbol:
                            matching_pos = pos
                            break
                    
                    if matching_pos:
                        # Update position data
                        managed_pos.current_price = matching_pos.mark_price
                        managed_pos.unrealized_pnl = matching_pos.unrealized_pnl
                        managed_pos.updated_at = datetime.now()
                        
                        # Check risk limits
                        await self._check_position_risk(position_id, managed_pos, connector)
                    else:
                        # Position closed, remove from management
                        logger.info("Position closed, removing from management", position_id=position_id)
                        del self.managed_positions[position_id]
        
        except Exception as e:
            logger.error("Failed to update positions", exchange=connector.exchange_name, error=str(e))
    
    async def _check_position_risk(self,
                                 position_id: str,
                                 position: ManagedPosition,
                                 connector: BaseExchangeConnector):
        """Check position against risk limits and take action if needed."""
        try:
            # Check stop loss
            if position.stop_loss_price and not position.max_loss_hit:
                should_close = False
                
                if position.side.lower() == 'long' and position.current_price <= position.stop_loss_price:
                    should_close = True
                elif position.side.lower() == 'short' and position.current_price >= position.stop_loss_price:
                    should_close = True
                
                if should_close:
                    await self._close_position_for_risk(position_id, position, connector, "stop_loss_hit")
                    return
            
            # Check take profit
            if position.take_profit_price:
                should_close = False
                
                if position.side.lower() == 'long' and position.current_price >= position.take_profit_price:
                    should_close = True
                elif position.side.lower() == 'short' and position.current_price <= position.take_profit_price:
                    should_close = True
                
                if should_close:
                    await self._close_position_for_risk(position_id, position, connector, "take_profit_hit")
                    return
            
            # Check maximum loss percentage
            if position.risk_metrics:
                current_loss_pct = abs(position.unrealized_pnl) / (position.size * position.entry_price) * 100
                if current_loss_pct > 10:  # 10% max loss
                    await self._close_position_for_risk(position_id, position, connector, "max_loss_exceeded")
        
        except Exception as e:
            logger.error("Position risk check failed", position_id=position_id, error=str(e))
    
    async def _close_position_for_risk(self,
                                     position_id: str,
                                     position: ManagedPosition,
                                     connector: BaseExchangeConnector,
                                     reason: str):
        """Close a position due to risk management."""
        try:
            from ..exchanges.base_connector import OrderSide
            
            # Determine close side (opposite of position side)
            close_side = OrderSide.SELL if position.side.lower() == 'long' else OrderSide.BUY
            
            # Create market order to close position
            close_order = await connector.create_market_order(
                symbol=position.symbol,
                side=close_side,
                amount=abs(position.size)
            )
            
            # Log risk management action
            log_risk_management(
                action="position_closed",
                reason=reason,
                symbol=position.symbol,
                exchange=position.exchange,
                position_id=position_id,
                unrealized_pnl=position.unrealized_pnl,
                close_order_id=close_order.id
            )
            
            # Record loss if applicable
            if position.unrealized_pnl < 0:
                self.risk_calculator.record_trade_loss(position.exchange, abs(position.unrealized_pnl))
            
            # Mark position as closed
            position.max_loss_hit = True
            
            logger.warning("Position closed for risk management",
                          position_id=position_id,
                          reason=reason,
                          symbol=position.symbol,
                          pnl=position.unrealized_pnl)
        
        except Exception as e:
            logger.error("Failed to close position for risk", position_id=position_id, error=str(e))
    
    async def _get_account_balance(self, connector: BaseExchangeConnector) -> float:
        """Get account balance for risk calculations."""
        try:
            balances = await connector.get_balance()
            
            # Get USDT balance or equivalent
            for currency in ['USDT', 'USD', 'BUSD', 'USDC']:
                if currency in balances:
                    return balances[currency].total
            
            # If no USD equivalent, return sum of all balances (simplified)
            return sum(balance.total for balance in balances.values())
        
        except Exception as e:
            logger.error("Failed to get account balance", error=str(e))
            return 10000.0  # Default fallback
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get summary of all managed positions."""
        try:
            total_positions = len(self.managed_positions)
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.managed_positions.values())
            
            # Group by exchange
            exchange_summary = {}
            for pos in self.managed_positions.values():
                if pos.exchange not in exchange_summary:
                    exchange_summary[pos.exchange] = {
                        'positions': 0,
                        'unrealized_pnl': 0.0,
                        'symbols': set()
                    }
                
                exchange_summary[pos.exchange]['positions'] += 1
                exchange_summary[pos.exchange]['unrealized_pnl'] += pos.unrealized_pnl
                exchange_summary[pos.exchange]['symbols'].add(pos.symbol)
            
            # Convert sets to lists for JSON serialization
            for exchange_data in exchange_summary.values():
                exchange_data['symbols'] = list(exchange_data['symbols'])
            
            return {
                'total_positions': total_positions,
                'total_unrealized_pnl': total_unrealized_pnl,
                'exchange_summary': exchange_summary,
                'position_limits': self.position_limits
            }
        
        except Exception as e:
            logger.error("Failed to get position summary", error=str(e))
            return {}
    
    def check_position_limits(self, symbol: str, exchange: str) -> Dict[str, Any]:
        """Check if new position would exceed limits."""
        try:
            # Count current positions
            total_positions = len(self.managed_positions)
            symbol_positions = sum(1 for pos in self.managed_positions.values() 
                                 if pos.symbol == symbol)
            
            # Check limits
            if total_positions >= self.position_limits['max_total_positions']:
                return {
                    'allowed': False,
                    'reason': 'max_total_positions_exceeded',
                    'current': total_positions,
                    'limit': self.position_limits['max_total_positions']
                }
            
            if symbol_positions >= self.position_limits['max_positions_per_symbol']:
                return {
                    'allowed': False,
                    'reason': 'max_symbol_positions_exceeded',
                    'current': symbol_positions,
                    'limit': self.position_limits['max_positions_per_symbol']
                }
            
            return {
                'allowed': True,
                'current_total': total_positions,
                'current_symbol': symbol_positions
            }
        
        except Exception as e:
            logger.error("Position limit check failed", error=str(e))
            return {'allowed': False, 'reason': 'check_error'}
    
    async def emergency_close_all(self, connector: BaseExchangeConnector, reason: str = "emergency"):
        """Emergency close all positions on an exchange."""
        try:
            positions_to_close = [
                (pos_id, pos) for pos_id, pos in self.managed_positions.items()
                if pos.exchange == connector.exchange_name
            ]
            
            for position_id, position in positions_to_close:
                await self._close_position_for_risk(position_id, position, connector, f"emergency_{reason}")
            
            log_risk_management(
                action="emergency_close_all",
                reason=reason,
                symbol="ALL",
                exchange=connector.exchange_name,
                positions_closed=len(positions_to_close)
            )
            
            logger.warning("Emergency close all positions executed",
                          exchange=connector.exchange_name,
                          positions_closed=len(positions_to_close),
                          reason=reason)
        
        except Exception as e:
            logger.error("Emergency close all failed", exchange=connector.exchange_name, error=str(e))


# Global position manager instance
position_manager = PositionManager()


def get_position_manager() -> PositionManager:
    """Get the global position manager instance."""
    return position_manager
