"""
Algorithmic order implementations: iceberg, grid, scaled, TWAP.
"""

from typing import Dict, Any, List
import asyncio
import math
from datetime import datetime, timedelta

from .order_factory import OrderExecutor
from ..webhook.parser import ParsedCommand
from ..exchanges.base_connector import OrderSide
from ..utils.logger import log_trade_execution


class IcebergOrderExecutor(OrderExecutor):
    """Executor for iceberg orders (large orders split into smaller visible pieces)."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute an iceberg order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'amount', 'sliceAmount'],
                optional=['price', 'offset', 'variance']
            )
            
            side = params['side']
            total_amount = float(params['amount'])
            slice_amount = float(params['sliceAmount'])
            
            # Calculate price
            current_price = await self.get_current_price(parsed_command.symbol)
            if 'price' in params:
                order_price = float(params['price'])
            elif 'offset' in params:
                order_price = self.calculate_price_from_offset(current_price, params['offset'], side)
            else:
                order_price = current_price  # Market price
            
            # Calculate number of slices
            num_slices = math.ceil(total_amount / slice_amount)
            variance = float(params.get('variance', 0))  # Price variance between slices
            
            iceberg_id = f"iceberg_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            # Create iceberg order structure
            iceberg_order = {
                "id": iceberg_id,
                "symbol": parsed_command.symbol,
                "side": side,
                "total_amount": total_amount,
                "slice_amount": slice_amount,
                "num_slices": num_slices,
                "base_price": order_price,
                "variance": variance,
                "executed_slices": 0,
                "remaining_amount": total_amount,
                "child_orders": [],
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Start iceberg execution
            asyncio.create_task(self._execute_iceberg_slices(iceberg_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='iceberg',
                side=side,
                amount=total_amount,
                price=order_price,
                order_id=iceberg_id,
                status='started',
                slices=num_slices
            )
            
            return {
                "status": "success",
                "order_type": "iceberg",
                "order": iceberg_order,
                "total_slices": num_slices,
                "slice_amount": slice_amount,
                "message": "Iceberg order started successfully"
            }
        
        except Exception as e:
            self.logger.error("Iceberg order execution failed", error=str(e))
            raise
    
    async def _execute_iceberg_slices(self, iceberg_order: Dict[str, Any]):
        """Execute iceberg order slices sequentially."""
        try:
            while iceberg_order["remaining_amount"] > 0 and iceberg_order["status"] == "active":
                # Calculate slice amount (last slice might be smaller)
                current_slice_amount = min(iceberg_order["slice_amount"], iceberg_order["remaining_amount"])
                
                # Calculate slice price with variance
                price_variance = iceberg_order["variance"] * (2 * (0.5 - hash(str(datetime.now())) % 100 / 100))
                slice_price = iceberg_order["base_price"] * (1 + price_variance / 100)
                
                # Create limit order for this slice
                order_side = OrderSide.BUY if iceberg_order["side"].lower() == 'buy' else OrderSide.SELL
                
                slice_order = await self.connector.create_limit_order(
                    symbol=iceberg_order["symbol"],
                    side=order_side,
                    amount=current_slice_amount,
                    price=slice_price
                )
                
                iceberg_order["child_orders"].append(slice_order)
                iceberg_order["executed_slices"] += 1
                iceberg_order["remaining_amount"] -= current_slice_amount
                
                self.logger.info("Iceberg slice executed",
                               iceberg_id=iceberg_order["id"],
                               slice_number=iceberg_order["executed_slices"],
                               amount=current_slice_amount,
                               price=slice_price)
                
                # Wait for slice to be filled or partially filled before next slice
                await self._wait_for_slice_execution(slice_order, iceberg_order)
                
                # Small delay between slices
                await asyncio.sleep(1)
            
            iceberg_order["status"] = "completed"
            self.logger.info("Iceberg order completed", iceberg_id=iceberg_order["id"])
        
        except Exception as e:
            iceberg_order["status"] = "error"
            self.logger.error("Iceberg execution failed", iceberg_id=iceberg_order["id"], error=str(e))
    
    async def _wait_for_slice_execution(self, slice_order, iceberg_order):
        """Wait for slice order to be executed or timeout."""
        timeout = 300  # 5 minutes timeout
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < timeout:
            try:
                order_status = await self.connector.get_order_status(slice_order.id, iceberg_order["symbol"])
                if order_status.status in ['closed', 'canceled']:
                    break
            except:
                pass
            
            await asyncio.sleep(5)  # Check every 5 seconds


class GridOrderExecutor(OrderExecutor):
    """Executor for grid trading orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a grid trading order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['gridLevels', 'priceRange'],
                optional=['amount', 'upperPrice', 'lowerPrice', 'gridSpacing']
            )
            
            grid_levels = int(params['gridLevels'])
            price_range = float(params['priceRange'])
            amount_per_grid = float(params.get('amount', 100))
            
            current_price = await self.get_current_price(parsed_command.symbol)
            
            # Calculate grid parameters
            if 'upperPrice' in params and 'lowerPrice' in params:
                upper_price = float(params['upperPrice'])
                lower_price = float(params['lowerPrice'])
            else:
                # Use price range around current price
                upper_price = current_price * (1 + price_range / 200)
                lower_price = current_price * (1 - price_range / 200)
            
            grid_spacing = (upper_price - lower_price) / (grid_levels - 1)
            
            grid_id = f"grid_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            grid_order = {
                "id": grid_id,
                "symbol": parsed_command.symbol,
                "grid_levels": grid_levels,
                "upper_price": upper_price,
                "lower_price": lower_price,
                "grid_spacing": grid_spacing,
                "amount_per_grid": amount_per_grid,
                "current_price": current_price,
                "buy_orders": [],
                "sell_orders": [],
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Create grid orders
            await self._create_grid_orders(grid_order)
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='grid',
                side='both',
                amount=amount_per_grid * grid_levels,
                price=current_price,
                order_id=grid_id,
                status='created',
                grid_levels=grid_levels
            )
            
            return {
                "status": "success",
                "order_type": "grid",
                "order": grid_order,
                "grid_levels": grid_levels,
                "price_range": f"{lower_price:.2f} - {upper_price:.2f}",
                "message": "Grid trading order created successfully"
            }
        
        except Exception as e:
            self.logger.error("Grid order execution failed", error=str(e))
            raise
    
    async def _create_grid_orders(self, grid_order: Dict[str, Any]):
        """Create buy and sell orders for grid trading."""
        try:
            current_price = grid_order["current_price"]
            
            # Create buy orders below current price
            for i in range(grid_order["grid_levels"]):
                grid_price = grid_order["lower_price"] + (i * grid_order["grid_spacing"])
                
                if grid_price < current_price:
                    # Create buy order
                    buy_order = await self.connector.create_limit_order(
                        symbol=grid_order["symbol"],
                        side=OrderSide.BUY,
                        amount=grid_order["amount_per_grid"],
                        price=grid_price
                    )
                    grid_order["buy_orders"].append(buy_order)
                
                elif grid_price > current_price:
                    # Create sell order
                    sell_order = await self.connector.create_limit_order(
                        symbol=grid_order["symbol"],
                        side=OrderSide.SELL,
                        amount=grid_order["amount_per_grid"],
                        price=grid_price
                    )
                    grid_order["sell_orders"].append(sell_order)
            
            # Start monitoring grid
            asyncio.create_task(self._monitor_grid_orders(grid_order))
        
        except Exception as e:
            self.logger.error("Grid order creation failed", error=str(e))
    
    async def _monitor_grid_orders(self, grid_order: Dict[str, Any]):
        """Monitor grid orders and recreate filled orders."""
        # Implementation for grid monitoring and order recreation
        pass


class ScaledOrderExecutor(OrderExecutor):
    """Executor for scaled (DCA) orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a scaled order (Dollar Cost Averaging)."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'totalAmount', 'levels'],
                optional=['priceRange', 'distribution', 'timeInterval']
            )
            
            side = params['side']
            total_amount = float(params['totalAmount'])
            levels = int(params['levels'])
            price_range = float(params.get('priceRange', 5))  # 5% default
            
            current_price = await self.get_current_price(parsed_command.symbol)
            
            # Calculate price levels
            if side.lower() == 'buy':
                # For buy orders, scale down in price
                price_step = (current_price * price_range / 100) / levels
                prices = [current_price - (i * price_step) for i in range(levels)]
            else:
                # For sell orders, scale up in price
                price_step = (current_price * price_range / 100) / levels
                prices = [current_price + (i * price_step) for i in range(levels)]
            
            # Calculate amounts per level (can be equal or weighted)
            distribution = params.get('distribution', 'equal')
            if distribution == 'equal':
                amounts = [total_amount / levels] * levels
            else:
                # Implement other distribution methods (pyramid, etc.)
                amounts = [total_amount / levels] * levels
            
            scaled_id = f"scaled_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            scaled_order = {
                "id": scaled_id,
                "symbol": parsed_command.symbol,
                "side": side,
                "total_amount": total_amount,
                "levels": levels,
                "prices": prices,
                "amounts": amounts,
                "orders": [],
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Create scaled orders
            await self._create_scaled_orders(scaled_order)
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='scaled',
                side=side,
                amount=total_amount,
                price=current_price,
                order_id=scaled_id,
                status='created',
                levels=levels
            )
            
            return {
                "status": "success",
                "order_type": "scaled",
                "order": scaled_order,
                "levels": levels,
                "price_range": f"{min(prices):.2f} - {max(prices):.2f}",
                "message": "Scaled order created successfully"
            }
        
        except Exception as e:
            self.logger.error("Scaled order execution failed", error=str(e))
            raise
    
    async def _create_scaled_orders(self, scaled_order: Dict[str, Any]):
        """Create individual orders for scaled execution."""
        try:
            order_side = OrderSide.BUY if scaled_order["side"].lower() == 'buy' else OrderSide.SELL
            
            for i, (price, amount) in enumerate(zip(scaled_order["prices"], scaled_order["amounts"])):
                order = await self.connector.create_limit_order(
                    symbol=scaled_order["symbol"],
                    side=order_side,
                    amount=amount,
                    price=price
                )
                scaled_order["orders"].append(order)
                
                self.logger.info("Scaled order level created",
                               scaled_id=scaled_order["id"],
                               level=i+1,
                               price=price,
                               amount=amount)
        
        except Exception as e:
            self.logger.error("Scaled order creation failed", error=str(e))


class TWAPOrderExecutor(OrderExecutor):
    """Executor for Time-Weighted Average Price orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a TWAP order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'amount', 'duration'],
                optional=['intervals', 'priceLimit', 'randomize']
            )
            
            side = params['side']
            total_amount = float(params['amount'])
            duration_minutes = int(params['duration'])  # Duration in minutes
            intervals = int(params.get('intervals', duration_minutes))  # Default: 1 order per minute
            
            # Calculate order parameters
            amount_per_interval = total_amount / intervals
            interval_seconds = (duration_minutes * 60) / intervals
            
            twap_id = f"twap_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            twap_order = {
                "id": twap_id,
                "symbol": parsed_command.symbol,
                "side": side,
                "total_amount": total_amount,
                "duration_minutes": duration_minutes,
                "intervals": intervals,
                "amount_per_interval": amount_per_interval,
                "interval_seconds": interval_seconds,
                "executed_intervals": 0,
                "orders": [],
                "status": "active",
                "created_at": datetime.now(),
                "randomize": params.get('randomize', False)
            }
            
            # Start TWAP execution
            asyncio.create_task(self._execute_twap_intervals(twap_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='twap',
                side=side,
                amount=total_amount,
                order_id=twap_id,
                status='started',
                duration=duration_minutes,
                intervals=intervals
            )
            
            return {
                "status": "success",
                "order_type": "twap",
                "order": twap_order,
                "duration_minutes": duration_minutes,
                "intervals": intervals,
                "amount_per_interval": amount_per_interval,
                "message": "TWAP order started successfully"
            }
        
        except Exception as e:
            self.logger.error("TWAP order execution failed", error=str(e))
            raise
    
    async def _execute_twap_intervals(self, twap_order: Dict[str, Any]):
        """Execute TWAP order intervals."""
        try:
            order_side = OrderSide.BUY if twap_order["side"].lower() == 'buy' else OrderSide.SELL
            
            for i in range(twap_order["intervals"]):
                if twap_order["status"] != "active":
                    break
                
                # Calculate interval amount (with optional randomization)
                interval_amount = twap_order["amount_per_interval"]
                if twap_order["randomize"]:
                    # Add ±10% randomization
                    randomization = 0.8 + (0.4 * (hash(str(datetime.now())) % 100 / 100))
                    interval_amount *= randomization
                
                # Execute market order for this interval
                order = await self.connector.create_market_order(
                    symbol=twap_order["symbol"],
                    side=order_side,
                    amount=interval_amount
                )
                
                twap_order["orders"].append(order)
                twap_order["executed_intervals"] += 1
                
                self.logger.info("TWAP interval executed",
                               twap_id=twap_order["id"],
                               interval=i+1,
                               amount=interval_amount)
                
                # Wait for next interval (except for last one)
                if i < twap_order["intervals"] - 1:
                    await asyncio.sleep(twap_order["interval_seconds"])
            
            twap_order["status"] = "completed"
            self.logger.info("TWAP order completed", twap_id=twap_order["id"])
        
        except Exception as e:
            twap_order["status"] = "error"
            self.logger.error("TWAP execution failed", twap_id=twap_order["id"], error=str(e))
