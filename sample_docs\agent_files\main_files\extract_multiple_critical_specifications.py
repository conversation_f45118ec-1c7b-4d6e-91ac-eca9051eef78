
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: extract_multiple_critical_specifications.py
# execution: true
from api_server.agent_tools.ask_question_about_documents import ask_question_about_documents
import json

print("Extracting complete specifications from multiple critical URLs...")
print("="*80)

# Define the most critical URLs that contain complete technical specifications
critical_spec_urls = [
    "https://alertatron.com/docs/automated-trading/market-order",
    "https://alertatron.com/docs/automated-trading/limit-order", 
    "https://alertatron.com/docs/automated-trading/stop-order",
    "https://alertatron.com/docs/automated-trading/trailing-stop-order",
    "https://alertatron.com/docs/automated-trading/understanding-amount-position",
    "https://alertatron.com/docs/automated-trading/common-examples",
    "https://alertatron.com/docs/automated-trading/api-keys-binance-spot",
    "https://alertatron.com/docs/automated-trading/api-keys-bybit"
]

# Extract complete specifications from each URL
complete_specs = {}

for i, url in enumerate(critical_spec_urls, 1):
    print(f"\n{i}. Extracting from: {url}")
    
    query = f"""
    Extract ALL complete technical specifications from this page:
    
    1. Complete command syntax with all parameters
    2. All parameter descriptions and requirements
    3. Example usage and code samples
    4. Special requirements or constraints
    5. Error handling information
    6. API integration details (if applicable)
    7. Any configuration or setup instructions
    
    Provide the complete technical reference for implementing this functionality in an algorithmic trading system.
    """
    
    try:
        csv_answer, json_answer, rag_answer, advanced_rag_answers = ask_question_about_documents(
            query=query,
            urls=[url]
        )
        
        # Store the complete specification
        page_name = url.split('/')[-1]
        complete_specs[page_name] = {
            'url': url,
            'rag_answer': rag_answer,
            'advanced_rag_answers': advanced_rag_answers
        }
        
        print(f"✅ Successfully extracted specifications from {page_name}")
        
    except Exception as e:
        print(f"❌ Failed to extract from {url}: {e}")

# Save all complete specifications
with open("alertatron_complete_specifications.json", "w", encoding='utf-8') as f:
    json.dump(complete_specs, f, indent=2, ensure_ascii=False)

# Create a comprehensive text summary
with open("alertatron_complete_specifications.txt", "w", encoding='utf-8') as f:
    f.write("ALERTATRON COMPLETE TECHNICAL SPECIFICATIONS\n")
    f.write("="*60 + "\n\n")
    f.write("EXTRACTION DATE: 2025-07-11\n")
    f.write("SOURCE: Multiple Alertatron Documentation Pages\n\n")
    
    for page_name, specs in complete_specs.items():
        f.write(f"\n{'='*60}\n")
        f.write(f"PAGE: {page_name}\n")
        f.write(f"URL: {specs['url']}\n")
        f.write(f"{'='*60}\n\n")
        
        if specs['rag_answer']:
            f.write("COMPLETE SPECIFICATIONS:\n")
            f.write("-" * 30 + "\n")
            f.write(specs['rag_answer'])
            f.write("\n\n")
        
        if specs['advanced_rag_answers']:
            f.write("ADVANCED SPECIFICATIONS:\n")
            f.write("-" * 30 + "\n")
            f.write(str(specs['advanced_rag_answers']))
            f.write("\n\n")

print("\n" + "="*80)
print("COMPLETE SPECIFICATIONS EXTRACTION SUMMARY")
print("="*80)
print(f"✅ Successfully extracted from {len(complete_specs)} critical pages")
print("✅ Saved JSON format: alertatron_complete_specifications.json")
print("✅ Saved text format: alertatron_complete_specifications.txt")
print("✅ Complete technical specifications ready for AI agent implementation")
print("="*80)