
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: extract_complete_technical_specs.py
# execution: true
from api_server.agent_tools.browser_agent import browser_agent

print("Extracting complete technical specifications using browser agent...")
print("="*80)

# Extract complete command reference with all order types
command_extraction_query = """
Navigate to the Alertatron command reference page and extract ALL complete technical specifications:

1. Go to https://alertatron.com/docs/automated-trading/command-reference
2. Extract the complete syntax and parameters for ALL order types including:
   - Market Order: complete syntax and all parameters
   - Limit Order: complete syntax and all parameters
   - Stop Order: complete syntax and all parameters
   - Trailing Stop Order: complete syntax and all parameters
   - TWAP Order: complete syntax and all parameters
   - Grid Order: complete syntax and all parameters
   - All other order types shown in the navigation

3. For each order type, extract:
   - Exact command syntax
   - All parameter names and descriptions
   - Example usage
   - Special requirements or constraints

4. Also extract any general command structure information, error handling, and best practices

Please provide the complete technical reference that would be needed to implement all trading commands in an algorithmic trading system.
"""

print("Extracting complete command reference...")
result = browser_agent(query=command_extraction_query)

# Save the complete command reference
with open("alertatron_complete_command_reference.txt", "w", encoding='utf-8') as f:
    f.write("ALERTATRON COMPLETE COMMAND REFERENCE\n")
    f.write("="*50 + "\n\n")
    f.write("EXTRACTION DATE: 2025-07-11\n")
    f.write("SOURCE: https://alertatron.com/docs/automated-trading/command-reference\n\n")
    f.write("COMPLETE COMMAND SPECIFICATIONS:\n")
    f.write("-" * 30 + "\n")
    f.write(result.get('text', 'No content extracted'))

print("="*80)
print("Complete command reference extracted and saved.")
print("File: alertatron_complete_command_reference.txt")
print("="*80)