
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: final_project_folder_creation.py
# execution: true
import os
import json
import shutil

print("Alertatron AI Agent proje klasörü oluşturuluyor...")
print("="*80)

# Ana proje klasörünü oluştur
project_folder = "Alertatron_AI_Agent_Project"
if not os.path.exists(project_folder):
    os.makedirs(project_folder)

# Alt klasörleri oluştur
folders = [
    "01_Documentation",
    "02_Technical_Specifications", 
    "03_Order_Types_Commands",
    "04_API_Integration",
    "05_Examples_and_Usage",
    "06_Screenshots_and_Visuals",
    "07_Extracted_Data",
    "08_Implementation_Guide"
]

for folder in folders:
    folder_path = os.path.join(project_folder, folder)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

print("1. Klasör yapısı oluşturuldu:")
for folder in folders:
    print(f"   ✅ {folder}")

# 2. Ana dokümantasyon dosyalarını kopyala
print("\n2. Ana dokümantasyon dosyaları kopyalanıyor...")

main_files = [
    "Alertatron_Complete_Technical_Documentation.pdf",
    "Alertatron_Final_Comprehensive_Documentation.pdf",
    "extracted_specifications.json",
    "alertatron_technical_specifications.txt"
]

copied_main = 0
for file_name in main_files:
    if os.path.exists(file_name):
        if file_name.endswith('.pdf'):
            target = os.path.join(project_folder, "01_Documentation")
        else:
            target = os.path.join(project_folder, "02_Technical_Specifications")
        shutil.copy(file_name, target)
        copied_main += 1
        print(f"   ✅ {file_name}")

# 3. JSON veri dosyalarını kopyala
print("\n3. Çıkarılan veri dosyaları kopyalanıyor...")

json_files = [
    "basic_concepts_20250711-144313_content.json",
    "command_reference_20250711-144724_content.json",
    "market_order_20250711-151348_content.json",
    "limit_order_20250711-151505_content.json"
]

copied_json = 0
for json_file in json_files:
    if os.path.exists(json_file):
        target = os.path.join(project_folder, "07_Extracted_Data")
        shutil.copy(json_file, target)
        copied_json += 1
        print(f"   ✅ {json_file}")

# 4. Ekran görüntülerini kopyala
print("\n4. Ekran görüntüleri kopyalanıyor...")

screenshots = [
    "basic_concepts_20250711-144614_page_0_outof_page_5.png",
    "basic_concepts_20250711-144615_page_1_outof_page_5.png"
]

copied_screens = 0
for screenshot in screenshots:
    if os.path.exists(screenshot):
        target = os.path.join(project_folder, "06_Screenshots_and_Visuals")
        shutil.copy(screenshot, target)
        copied_screens += 1
        print(f"   ✅ {screenshot}")

print(f"\n📁 Toplam kopyalanan dosya: {copied_main + copied_json + copied_screens}")

# 5. Detaylı emir tipi dokümantasyonu oluştur
print("\n5. Detaylı emir tipi dokümantasyonu oluşturuluyor...")

# Ana emir tipleri
order_types = {
    "market_order": {
        "name": "Market Order",
        "syntax": "market(side, amount, position, reduceOnly)",
        "description": "Anında piyasa emri",
        "required_params": ["side"],
        "optional_params": ["amount", "position", "reduceOnly"]
    },
    "limit_order": {
        "name": "Limit Order", 
        "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)",
        "description": "Belirli fiyattan limit emri",
        "required_params": ["side", "amount", "offset"],
        "optional_params": ["postOnly", "reduceOnly", "position", "tag"]
    },
    "stop_order": {
        "name": "Stop Order",
        "syntax": "stop(side, amount, offset, ...)",
        "description": "Stop emri"
    },
    "trailing_stop": {
        "name": "Trailing Stop",
        "syntax": "trailingStop(...)",
        "description": "Takip eden stop emri"
    },
    "iceberg_order": {
        "name": "Iceberg Order",
        "syntax": "iceberg(...)",
        "description": "Buzdağı emri"
    },
    "grid_order": {
        "name": "Grid Order",
        "syntax": "grid(...)",
        "description": "Izgara emri"
    },
    "twap_order": {
        "name": "TWAP Order",
        "syntax": "twap(...)",
        "description": "Zaman ağırlıklı ortalama"
    }
}

# Emir tiplerini kaydet
order_file = os.path.join(project_folder, "03_Order_Types_Commands", "order_types_detailed.json")
with open(order_file, 'w', encoding='utf-8') as f:
    json.dump(order_types, f, indent=2, ensure_ascii=False)

print(f"   ✅ {len(order_types)} emir tipi detaylandırıldı")

# 6. API entegrasyon bilgileri
print("\n6. API entegrasyon rehberi oluşturuluyor...")

api_info = {
    "exchanges": [
        "Binance Spot", "Binance Futures", "Bybit", "OKX", 
        "BitMEX", "Bitfinex", "Bitget", "Deribit", "Binance US"
    ],
    "webhook_format": "MyKeys(SYMBOL) { command(params); } #bot",
    "requirements": [
        "API anahtarları kurulumu",
        "Trading izinleri",
        "IP kısıtlamaları kaldırma"
    ]
}

api_file = os.path.join(project_folder, "04_API_Integration", "api_guide.json")
with open(api_file, 'w', encoding='utf-8') as f:
    json.dump(api_info, f, indent=2, ensure_ascii=False)

# 7. README dosyası oluştur
print("\n7. README dosyası oluşturuluyor...")

readme_content = "# ALERTATRON AI AGENT PROJECT\n\n"
readme_content += "Bu klasor Alertatron platformu icin AI agent gelistirmek\n"
readme_content += "icin gerekli tum teknik bilgileri icerir.\n\n"
readme_content += "## Klasor Yapisi:\n\n"
readme_content += "01_Documentation - Ana PDF dokumanlar\n"
readme_content += "02_Technical_Specifications - Teknik ozellikler\n"
readme_content += "03_Order_Types_Commands - Tum emir tipleri\n"
readme_content += "04_API_Integration - API entegrasyon bilgileri\n"
readme_content += "05_Examples_and_Usage - Kullanim ornekleri\n"
readme_content += "06_Screenshots_and_Visuals - Ekran goruntuleri\n"
readme_content += "07_Extracted_Data - Cikartilan ham veriler\n"
readme_content += "08_Implementation_Guide - Python implementasyon rehberi\n\n"
readme_content += "## Kullanim:\n\n"
readme_content += "1. 01_Documentation klasorundeki PDF'leri inceleyin\n"
readme_content += "2. 03_Order_Types_Commands klasorunde tum emir tiplerini gorun\n"
readme_content += "3. Python kodu yazmak icin 08_Implementation_Guide'i kullanin\n"

readme_file = os.path.join(project_folder, "README.md")
with open(readme_file, 'w', encoding='utf-8') as f:
    f.write(readme_content)

print("   ✅ README.md oluşturuldu")

print("="*80)
print("🎉 ALERTATRON AI AGENT PROJESİ KLASÖRÜ HAZIR!")
print("="*80)
print(f"📁 Klasör konumu: ./{project_folder}/")
print(f"📋 Toplam klasör sayısı: {len(folders)}")
print(f"📄 Toplam dosya sayısı: {copied_main + copied_json + copied_screens + 4}")
print("\n🚀 Yapay zeka ajanınız bu klasörü analiz ederek")
print("🚀 Python'da algoritmik trading projesi yazabilir!")
print("="*80)