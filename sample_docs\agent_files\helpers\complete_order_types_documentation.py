
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: complete_order_types_documentation.py
# execution: true
import os
import json

print("Eksik emir tiplerini tamamlıyorum...")
print("="*60)

project_folder = "Alertatron_AI_Agent_Project"
order_types_folder = os.path.join(project_folder, "03_Order_Types_Commands")

# Daha önce analiz ettiğim TÜM emir tiplerini dahil et
complete_order_types = {
    "market_order": {
        "name": "Market Order (Piyasa Emri)",
        "syntax": "market(side, amount, position, reduceOnly)",
        "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri",
        "parameters": {
            "side": {"type": "string", "required": True, "values": ["buy", "sell"], "default": "buy"},
            "amount": {"type": "mixed", "formats": ["1000", "50%", "50%x", "50%p", "50%b", "50%a"]},
            "position": {"type": "mixed", "description": "Hedef pozisyon büyüklüğü", "examples": ["position=1000", "position=0", "position=-500"]},
            "reduceOnly": {"type": "boolean", "description": "Sadece mevcut pozisyonu azaltmak için"}
        },
        "examples": ["market(side='buy', amount=1000)", "market(position=0)", "market(side='sell', amount='50%')"]
    },
    "limit_order": {
        "name": "Limit Order (Limit Emri)",
        "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)",
        "description": "Belirli fiyattan limit emri yerleştirir",
        "parameters": {
            "side": {"type": "string", "required": True, "values": ["buy", "sell"]},
            "amount": {"type": "number", "required": "position kullanılmıyorsa zorunlu"},
            "offset": {"type": "string", "required": True, "default": "1%", "formats": ["50", "1%", "e50", "e1%", "@50000"]},
            "postOnly": {"type": "boolean", "default": True, "description": "Sadece post emri"},
            "reduceOnly": {"type": "boolean", "description": "Sadece pozisyon azaltma"},
            "position": {"type": "mixed", "description": "Pozisyon tabanlı boyutlandırma"},
            "tag": {"type": "string", "description": "Emir tanımlayıcı etiketi"}
        },
        "examples": ["limit('buy', 1000, '1%', true)", "limit('sell', '50%', 'e1%', true)"]
    },
    "stop_order": {
        "name": "Stop Order (Stop Emri)",
        "syntax": "stop(side, amount, offset, postOnly, reduceOnly, position, tag)",
        "description": "Belirli fiyat seviyesine ulaşıldığında tetiklenen emir",
        "use_cases": ["Stop-loss emirleri", "Breakout stratejileri", "Risk yönetimi"],
        "parameters": "Limit order ile benzer parametreler kullanır"
    },
    "stop_or_take_profit_order": {
        "name": "Stop or Take Profit Order",
        "syntax": "stopOrTakeProfit(side, amount, stopPrice, takeProfitPrice, ...)",
        "description": "Stop-loss ve take-profit seviyelerini birlikte belirleyen emir",
        "use_cases": ["Otomatik risk yönetimi", "Kar koruma", "Kayıp sınırlama"]
    },
    "trailing_stop_order": {
        "name": "Trailing Stop Order (Takip Eden Stop)",
        "syntax": "trailingStop(side, amount, offset, ...)",
        "description": "Fiyat lehte hareket ettikçe stop seviyesini otomatik takip eden emir",
        "benefits": ["Kar koruma", "Trend takibi", "Otomatik risk yönetimi"]
    },
    "trailing_take_profit": {
        "name": "Trailing Take Profit (Takip Eden Kar Al)",
        "syntax": "trailingTakeProfit(side, amount, offset, ...)",
        "description": "Kar seviyesini fiyat hareketlerine göre otomatik ayarlayan emir",
        "benefits": ["Maksimum kar yakalama", "Trend devamında pozisyon tutma"]
    },
    "trailing_limit_order": {
        "name": "Trailing Limit Order",
        "syntax": "trailingLimit(side, amount, offset, ...)",
        "description": "Fiyat hareketini takip eden limit emir"
    },
    "iceberg_order": {
        "name": "Iceberg Order (Buzdağı Emri)",
        "syntax": "iceberg(side, amount, sliceAmount, ...)",
        "description": "Büyük emirleri küçük parçalara bölerek piyasa etkisini azaltan emir",
        "purpose": "Büyük emirlerin piyasa etkisini minimize etme",
        "parameters": {
            "sliceAmount": "Her seferinde gösterilen emir miktarı"
        }
    },
    "grid_order": {
        "name": "Grid Order (Izgara Emri)",
        "syntax": "grid(side, amount, gridLevels, priceRange, ...)",
        "description": "Belirli fiyat aralıklarında otomatik alım-satım yapan emir",
        "strategy": "Range trading ve volatilite ticareti",
        "parameters": {
            "gridLevels": "Izgara seviye sayısı",
            "priceRange": "Fiyat aralığı"
        }
    },
    "scaled_order": {
        "name": "Scaled Order (Kademeli Emir)",
        "syntax": "scaled(side, totalAmount, levels, priceRange, ...)",
        "description": "Toplam miktarı farklı fiyat seviyelerine bölen kademeli emir",
        "use_cases": ["DCA stratejisi", "Ortalama maliyet düşürme"]
    },
    "twap_order": {
        "name": "TWAP Order (Zaman Ağırlıklı Ortalama Fiyat)",
        "syntax": "twap(side, amount, duration, intervals, ...)",
        "description": "Belirli zaman diliminde emirleri eşit aralıklarla bölen emir",
        "advantage": "Piyasa etkisini minimize etme",
        "parameters": {
            "duration": "Toplam süre",
            "intervals": "Emir aralıkları"
        }
    },
    "ping_pong_order": {
        "name": "Ping Pong Order",
        "syntax": "pingPong(side, amount, range, profitMargin, ...)",
        "description": "Belirli fiyat aralığında sürekli alım-satım yaparak kar elde etme",
        "strategy": "Sideways market'lerde kar elde etme"
    },
    "market_maker_order": {
        "name": "Market Maker Order",
        "syntax": "marketMaker(spread, amount, ...)",
        "description": "Alış-satış emirlerini eşzamanlı vererek spread'den kar elde etme",
        "strategy": "Likidite sağlama ve spread arbitrajı"
    },
    "aggressive_entry_order": {
        "name": "Aggressive Entry Order",
        "syntax": "aggressiveEntry(side, amount, slippage, ...)",
        "description": "Pozisyona hızlı giriş için agresif emir stratejisi",
        "use_cases": ["Breakout ticareti", "Momentum yakalama"]
    },
    "dynamic_take_profit": {
        "name": "Dynamic Take Profit",
        "syntax": "dynamicTakeProfit(side, amount, levels, conditions, ...)",
        "description": "Piyasa koşullarına göre dinamik kar alma seviyesi",
        "parameters": {
            "levels": "Farklı kar alma seviyeler",
            "conditions": "Tetikleme koşulları"
        }
    },
    "one_cancels_other": {
        "name": "One Cancels Other (OCO)",
        "syntax": "oco(order1, order2)",
        "description": "İki emirden biri gerçekleştiğinde diğerini iptal eden emir çifti",
        "use_cases": ["Stop-loss ve take-profit kombinasyonu", "İki yönlü strateji"]
    },
    "waiting_limit_order": {
        "name": "Waiting Limit Order",
        "syntax": "waitingLimit(side, amount, condition, timeout, ...)",
        "description": "Belirli koşullar gerçekleşene kadar bekleyen limit emir",
        "parameters": {
            "condition": "Bekleme koşulu",
            "timeout": "Maksimum bekleme süresi"
        }
    },
    "managed_order": {
        "name": "Managed Order",
        "syntax": "managed(strategy, parameters, ...)",
        "description": "Otomatik yönetilen algoritmik emir stratejisi"
    }
}

# Tüm emir tiplerini kaydet
complete_file = os.path.join(order_types_folder, "all_order_types_complete.json")
with open(complete_file, 'w', encoding='utf-8') as f:
    json.dump(complete_order_types, f, indent=2, ensure_ascii=False)

# Özet bilgi dosyası oluştur
summary = {
    "total_order_types": len(complete_order_types),
    "categories": {
        "basic_orders": ["market_order", "limit_order", "stop_order"],
        "risk_management": ["stop_or_take_profit_order", "trailing_stop_order", "trailing_take_profit"],
        "algorithmic_orders": ["iceberg_order", "grid_order", "twap_order", "scaled_order"],
        "advanced_strategies": ["ping_pong_order", "market_maker_order", "aggressive_entry_order"],
        "dynamic_orders": ["dynamic_take_profit", "managed_order"],
        "conditional_orders": ["one_cancels_other", "waiting_limit_order"]
    },
    "implementation_priority": [
        "market_order", "limit_order", "stop_order", 
        "trailing_stop_order", "iceberg_order", "grid_order"
    ]
}

summary_file = os.path.join(order_types_folder, "order_types_summary.json")
with open(summary_file, 'w', encoding='utf-8') as f:
    json.dump(summary, f, indent=2, ensure_ascii=False)

print(f"✅ {len(complete_order_types)} emir tipi tamamlandı")
print(f"✅ Kategorize edilmiş özet oluşturuldu")
print(f"✅ Dosyalar: all_order_types_complete.json, order_types_summary.json")

