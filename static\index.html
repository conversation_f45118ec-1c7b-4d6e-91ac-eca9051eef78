<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alertatron AI Agent - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                Alertatron AI Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#exchanges">Exchanges</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#orders">Orders</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#risk">Risk</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#webhook-test">Test</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            System Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="status-item">
                                    <div class="status-indicator" id="system-status"></div>
                                    <span>System</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <div class="status-indicator" id="exchange-status"></div>
                                    <span>Exchanges</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <div class="status-indicator" id="webhook-status"></div>
                                    <span>Webhooks</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <div class="status-indicator" id="risk-status"></div>
                                    <span>Risk Management</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="exchanges-tab" data-bs-toggle="tab" data-bs-target="#exchanges" type="button">
                    <i class="fas fa-exchange-alt me-2"></i>Exchanges
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button">
                    <i class="fas fa-list-alt me-2"></i>Orders
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="risk-tab" data-bs-toggle="tab" data-bs-target="#risk" type="button">
                    <i class="fas fa-shield-alt me-2"></i>Risk Management
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="webhook-test-tab" data-bs-toggle="tab" data-bs-target="#webhook-test" type="button">
                    <i class="fas fa-vial me-2"></i>Webhook Test
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabContent">
            <!-- Dashboard Tab -->
            <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">System Information</h6>
                            </div>
                            <div class="card-body">
                                <div id="system-info">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Supported Order Types</h6>
                            </div>
                            <div class="card-body">
                                <div id="order-types">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Exchanges Tab -->
            <div class="tab-pane fade" id="exchanges" role="tabpanel">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Exchange Connections</h6>
                                <button class="btn btn-sm btn-primary" onclick="testExchanges()">
                                    <i class="fas fa-sync me-1"></i>Test All
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="exchange-list">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Account Balances</h6>
                            </div>
                            <div class="card-body">
                                <div id="balances">
                                    <button class="btn btn-outline-primary btn-sm" onclick="loadBalances()">
                                        <i class="fas fa-wallet me-1"></i>Load Balances
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Open Positions</h6>
                            </div>
                            <div class="card-body">
                                <div id="positions">
                                    <button class="btn btn-outline-primary btn-sm" onclick="loadPositions()">
                                        <i class="fas fa-chart-line me-1"></i>Load Positions
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders Tab -->
            <div class="tab-pane fade" id="orders" role="tabpanel">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Order Types Implementation Status</h6>
                            </div>
                            <div class="card-body">
                                <div id="order-implementation-status">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Risk Management Tab -->
            <div class="tab-pane fade" id="risk" role="tabpanel">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Risk Management Summary</h6>
                                <div>
                                    <button class="btn btn-sm btn-warning me-2" onclick="showEmergencyCloseModal()">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Emergency Close
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="loadRiskSummary()">
                                        <i class="fas fa-sync me-1"></i>Refresh
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="risk-summary">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Webhook Test Tab -->
            <div class="tab-pane fade" id="webhook-test" role="tabpanel">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Webhook Test</h6>
                            </div>
                            <div class="card-body">
                                <form id="webhook-test-form">
                                    <div class="mb-3">
                                        <label for="webhook-message" class="form-label">Webhook Message</label>
                                        <textarea class="form-control" id="webhook-message" rows="3" 
                                                placeholder="MyKeys(BTCUSDT) { market(side=buy, amount=100); } #bot"></textarea>
                                        <div class="form-text">
                                            Format: KeyName(SYMBOL) { command(parameters); } #bot
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-primary me-2" onclick="testWebhook()">
                                            <i class="fas fa-play me-1"></i>Test Webhook
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="loadExampleWebhooks()">
                                            <i class="fas fa-lightbulb me-1"></i>Load Examples
                                        </button>
                                    </div>
                                </form>
                                <div id="webhook-test-result"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Close Modal -->
    <div class="modal fade" id="emergencyCloseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Emergency Close Positions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will close ALL positions on the selected exchange immediately!
                    </div>
                    <div class="mb-3">
                        <label for="emergency-exchange" class="form-label">Select Exchange</label>
                        <select class="form-select" id="emergency-exchange">
                            <option value="">Select exchange...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="emergency-reason" class="form-label">Reason</label>
                        <input type="text" class="form-control" id="emergency-reason" placeholder="Emergency reason">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="executeEmergencyClose()">
                        <i class="fas fa-exclamation-triangle me-1"></i>Emergency Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
