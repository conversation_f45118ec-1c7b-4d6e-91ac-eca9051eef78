# ALERTATRON AI AGENT PYTHON IMPLEMENTATION GUIDE

## Project Structure

- main.py: <PERSON>
- webhook_handler.py: TradingView webhook isleyicisi
- command_parser.py: Alertatron komut ayristirici
- exchange_connectors/: Borsa baglantilari
- order_executors/: Emir yurutuculeri
- risk_management.py: Risk yonetimi

## Webhook Format

MyKeys(SYMBOL) { command(parameters); } #bot

## Order Types

Total order types available: 18

- Market Order (Piyasa Emri): market(side, amount, position, reduceOnly)
- Limit Order (Limit Emri): limit(side, amount, offset, postOnly, reduceOnly, position, tag)
- Stop Order (Stop Emri): stop(side, amount, offset, ...)
- Stop or Take Profit Order: stopOrTakeProfit(...)
- Trailing Stop Order: trailingStop(...)
- Trailing Take Profit: trailingTakeProfit(...)
- Trailing Limit Order: trailingLimit(...)
- Iceberg Order (Buzdağı Emri): iceberg(...)
- Grid Order (Izgara Emri): grid(...)
- Scaled Order (Kademeli Emir): scaled(...)
- TWAP Order: twap(...)
- Ping Pong Order: pingPong(...)
- Market Maker Order: marketMaker(...)
- Aggressive Entry Order: aggressiveEntry(...)
- Dynamic Take Profit: dynamicTakeProfit(...)
- One Cancels Other (OCO): oco(...)
- Waiting Limit Order: waitingLimit(...)
- Managed Order: managed(...)
