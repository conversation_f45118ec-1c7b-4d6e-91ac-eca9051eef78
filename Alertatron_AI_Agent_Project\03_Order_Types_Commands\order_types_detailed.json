{"market_order": {"name": "Market Order", "syntax": "market(side, amount, position, reduceOnly)", "description": "<PERSON><PERSON>nda piyasa emri", "required_params": ["side"], "optional_params": ["amount", "position", "reduceOnly"]}, "limit_order": {"name": "Limit Order", "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Belirli fiyattan limit emri", "required_params": ["side", "amount", "offset"], "optional_params": ["postOnly", "reduceOnly", "position", "tag"]}, "stop_order": {"name": "Stop Order", "syntax": "stop(side, amount, offset, ...)", "description": "Stop emri"}, "trailing_stop": {"name": "Trailing Stop", "syntax": "trailingStop(...)", "description": "Takip eden stop emri"}, "iceberg_order": {"name": "Iceberg Order", "syntax": "iceberg(...)", "description": "Buzdağı emri"}, "grid_order": {"name": "Grid Order", "syntax": "grid(...)", "description": "<PERSON><PERSON><PERSON> em<PERSON>"}, "twap_order": {"name": "TWAP Order", "syntax": "twap(...)", "description": "Zaman ağırlıklı ortalama"}}