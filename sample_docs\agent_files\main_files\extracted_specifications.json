{"market_order": {"syntax": "market(side, amount, position, reduceOnly)", "description": "Places a market order that is executed immediately at the current price", "parameters": {"side": "Required. 'buy' or 'sell', defaults to 'buy'", "amount": "Various formats: absolute (1000), percentage (50%), available balance (50%x), position (50%p)", "position": "Alternative to amount - target position size with automatic calculation", "reduceOnly": "Optional boolean - order only reduces existing position"}, "examples": ["market(side='buy', amount=1000)", "market(side='sell', amount='50%')", "market(position=1000)", "market(position=0)  // Close position"]}, "limit_order": {"syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Places a standard limit order. Command finishes as soon as order is placed", "parameters": {"side": "Required. Order direction (buy/sell)", "amount": "Required if position not used. Absolute amount, percentage formats supported", "offset": "Required. Price offset from current price. Formats: 50, 1%, e50, e1%, @50000", "postOnly": "Optional boolean, default true. Submit as post-only order", "reduceOnly": "Optional boolean. Order only reduces existing position", "position": "Optional. Position-based order sizing", "tag": "Optional string. Order identification tag"}, "examples": ["limit('buy', 1000, '1%', true)", "limit('sell', '50%', 'e1%', true)", "limit('buy', '25%x', '@50000', false)"]}}