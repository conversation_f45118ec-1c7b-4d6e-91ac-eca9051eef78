# Alertatron AI Agent Configuration

# Application Settings
DEBUG=false
HOST=0.0.0.0
PORT=8000
SECRET_KEY=your-secret-key-here
WEBHOOK_SECRET=your-webhook-secret-here

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/alertatron
REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/alertatron.log

# Trading Configuration
MAX_CONCURRENT_ORDERS=100
DEFAULT_TIMEOUT=30
MAX_POSITION_SIZE=10000.0
MAX_DAILY_LOSS=1000.0

# Security
ENCRYPTION_KEY=your-encryption-key-here

# API Keys - Example format for each exchange
# Replace 'MyKeys' with your actual key set name

# Binance Spot
MYKEYS_BINANCE_API_KEY=your-binance-api-key
MYKEYS_BINANCE_SECRET=your-binance-secret
MYKEYS_BINANCE_SANDBOX=false

# Binance Futures (USDM)
MYKEYS_BINANCEUSDM_API_KEY=your-binance-futures-api-key
MYKEYS_BINANCEUSDM_SECRET=your-binance-futures-secret
MYKEYS_BINANCEUSDM_SANDBOX=false

# Binance US
MYKEYS_BINANCEUS_API_KEY=your-binance-us-api-key
MYKEYS_BINANCEUS_SECRET=your-binance-us-secret
MYKEYS_BINANCEUS_SANDBOX=false

# Bybit
MYKEYS_BYBIT_API_KEY=your-bybit-api-key
MYKEYS_BYBIT_SECRET=your-bybit-secret
MYKEYS_BYBIT_SANDBOX=false

# OKX
MYKEYS_OKX_API_KEY=your-okx-api-key
MYKEYS_OKX_SECRET=your-okx-secret
MYKEYS_OKX_PASSPHRASE=your-okx-passphrase
MYKEYS_OKX_SANDBOX=false

# BitMEX
MYKEYS_BITMEX_API_KEY=your-bitmex-api-key
MYKEYS_BITMEX_SECRET=your-bitmex-secret
MYKEYS_BITMEX_SANDBOX=false

# Bitfinex
MYKEYS_BITFINEX_API_KEY=your-bitfinex-api-key
MYKEYS_BITFINEX_SECRET=your-bitfinex-secret
MYKEYS_BITFINEX_SANDBOX=false

# Bitget
MYKEYS_BITGET_API_KEY=your-bitget-api-key
MYKEYS_BITGET_SECRET=your-bitget-secret
MYKEYS_BITGET_PASSPHRASE=your-bitget-passphrase
MYKEYS_BITGET_SANDBOX=false

# Deribit
MYKEYS_DERIBIT_API_KEY=your-deribit-api-key
MYKEYS_DERIBIT_SECRET=your-deribit-secret
MYKEYS_DERIBIT_SANDBOX=false

# Additional Key Sets (you can have multiple key sets)
# TESTKEYS_BINANCE_API_KEY=your-test-binance-api-key
# TESTKEYS_BINANCE_SECRET=your-test-binance-secret
# TESTKEYS_BINANCE_SANDBOX=true
