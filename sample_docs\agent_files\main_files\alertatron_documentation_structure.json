{"timestamp": "2025-07-11T14:57:53.287523", "source_website": "https://alertatron.com/docs/automated-trading/basic-concepts", "extraction_summary": {"total_pages_analyzed": 9, "total_links_extracted": 495, "total_images_extracted": 31, "extraction_files": ["./basic_concepts_20250711-144313_content.json", "./command_reference_20250711-144724_content.json", "./common_examples_20250711-144949_content.json", "./automated_trading_examples_20250711-145008_content.json", "./getting_alerts_from_tradingview_20250711-145029_content.json", "./api_keys_binance_spot_20250711-145349_content.json", "./api_keys_binance_futures_20250711-145410_content.json", "./api_keys_bybit_20250711-145430_content.json", "./api_keys_okx_20250711-145452_content.json"]}, "content_categories": {"basic_concepts": "Platform overview and fundamental concepts", "command_reference": "Complete trading command syntax and parameters", "examples": "Practical implementation examples and code samples", "api_integration": "Exchange API setup and integration procedures", "tradingview_integration": "TradingView webhook integration methods"}, "supported_exchanges": ["Binance Spot", "Binance Futures", "Binance US", "Bybit", "OKX", "Bitfinex Spot and Margin", "BitMEX", "Bitget", "Deribit Futures"], "order_types": ["Market Order", "Limit Order", "Stop Order", "Trailing Stop Order", "Trailing Take Profit Order", "Iceberg Order", "Grid Order", "Scaled Order", "TWAP Order", "Ping Pong Order", "Market Maker Order", "One Cancels Other", "Managed Order", "Aggressive Entry Order", "Dynamic Take Profit"]}