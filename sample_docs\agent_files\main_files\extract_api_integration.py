
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: extract_api_integration.py
# execution: true
from api_server.agent_tools.ask_question_about_documents import ask_question_about_documents

# Extract API integration information from major exchanges
api_integration_urls = [
    "https://alertatron.com/docs/automated-trading/api-keys-binance-spot",
    "https://alertatron.com/docs/automated-trading/api-keys-binance-futures",
    "https://alertatron.com/docs/automated-trading/api-keys-bybit",
    "https://alertatron.com/docs/automated-trading/api-keys-okx"
]

print("Extracting API integration information from major exchanges...")
print("="*80)

# Extract comprehensive API setup and integration information
api_query = """
Extract ALL API integration information including:
1. Complete API key setup procedures for each exchange
2. Required permissions and settings
3. Authentication methods and security requirements
4. API rate limits and constraints
5. Exchange-specific parameters and configurations
6. Code examples for API integration
7. Error handling and troubleshooting for API connections
8. Testing procedures for API connectivity
9. Exchange-specific order types and limitations
10. Integration with Alertatron webhook system
11. Complete step-by-step setup guides
12. Common issues and solutions

Provide complete technical specifications for each exchange integration.
"""

print("3. Extracting API integration information for major exchanges...")
csv_answer, json_answer, rag_answer, advanced_rag_answers = ask_question_about_documents(
    query=api_query,
    urls=api_integration_urls
)

print("="*80)
print("API integration extraction completed.")
print("="*80)