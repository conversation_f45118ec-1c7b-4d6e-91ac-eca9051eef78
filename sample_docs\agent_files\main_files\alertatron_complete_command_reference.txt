ALERTATRON COMPLETE COMMAND REFERENCE
==================================================

EXTRACTION DATE: 2025-07-11
SOURCE: https://alertatron.com/docs/automated-trading/command-reference

COMPLETE COMMAND SPECIFICATIONS:
------------------------------
Could not cache non-existence of file. Will ignore error and continue. Error: [Errno 13] Permission denied: '/workspace/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/.no_exist/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/adapter_config.json'
Ignored error while writing commit hash to /workspace/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/refs/main: [<PERSON><PERSON><PERSON> 13] Permission denied: '/workspace/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/refs/main'.
- No cookies file provided. <PERSON><PERSON><PERSON> will not load any cookies.
- No cookies file found at None. Skipping cookie loading.
Using vision model: claude-sonnet-4-********-reasoning
Using full anthropic
INFO     [agent] 🧠 Starting an agent with main_model=claude-sonnet-4-********-reasoning +vision +memory, planner_model=None, extraction_model=None 
ERROR    [agent] 

❌  LLM ChatAnthropic connection test failed. Check that ANTHROPIC_API_KEY is set correctly in .env and that the LLM API account has sufficient funding.

Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-sonnet-4-********-reasoning'}}

ERROR    [huggingface_hub.file_download] Could not cache non-existence of file. Will ignore error and continue. Error: [Errno 13] Permission denied: '/workspace/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/.no_exist/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/adapter_config.json'
WARNING  [huggingface_hub._snapshot_download] Ignored error while writing commit hash to /workspace/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/refs/main: [Errno 13] Permission denied: '/workspace/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/refs/main'.
INFO     [mem0.vector_stores.faiss] Loaded FAISS index from /tmp/mem0_384_faiss/mem0.faiss with 0 vectors
INFO     [mem0.vector_stores.faiss] Loaded FAISS index from /workspace/.mem0/migrations_faiss/mem0_migrations.faiss with 18 vectors
INFO     [mem0.vector_stores.faiss] Inserted 1 vectors into collection mem0_migrations
INFO     [agent] 🚀 Starting task: 
Navigate to the Alertatron command reference page and extract ALL complete technical specifications:

1. Go to https://alertatron.com/docs/automated-trading/command-reference
2. Extract the complete syntax and parameters for ALL order types including:
   - Market Order: complete syntax and all parameters
   - Limit Order: complete syntax and all parameters
   - Stop Order: complete syntax and all parameters
   - Trailing Stop Order: complete syntax and all parameters
   - TWAP Order: complete syntax and all parameters
   - Grid Order: complete syntax and all parameters
   - All other order types shown in the navigation

3. For each order type, extract:
   - Exact command syntax
   - All parameter names and descriptions
   - Example usage
   - Special requirements or constraints

4. Also extract any general command structure information, error handling, and best practices

Please provide the complete technical reference that would be needed to implement all trading commands in an algorithmic trading system.

INFO     [agent] 📍 Step 1
ERROR    [browser] Failed to initialize Playwright browser: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [browser] ❌  Failed to create new browser session: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ (did the browser process quit?)
WARNING  [browser] ⚠️  Page load failed, continuing...
ERROR    [browser] Failed to initialize Playwright browser: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [browser] ❌  Failed to create new browser session: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ (did the browser process quit?)
ERROR    [agent] ❌ Result failed 1/3 times:
 BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
INFO     [agent] 📍 Step 1
ERROR    [browser] Failed to initialize Playwright browser: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [browser] ❌  Failed to create new browser session: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ (did the browser process quit?)
WARNING  [browser] ⚠️  Page load failed, continuing...
ERROR    [browser] Failed to initialize Playwright browser: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [browser] ❌  Failed to create new browser session: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ (did the browser process quit?)
ERROR    [agent] ❌ Result failed 2/3 times:
 BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
INFO     [agent] 📍 Step 1
ERROR    [browser] Failed to initialize Playwright browser: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [browser] ❌  Failed to create new browser session: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ (did the browser process quit?)
WARNING  [browser] ⚠️  Page load failed, continuing...
ERROR    [browser] Failed to initialize Playwright browser: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [browser] ❌  Failed to create new browser session: BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ (did the browser process quit?)
ERROR    [agent] ❌ Result failed 3/3 times:
 BrowserType.launch: Executable doesn't exist at /workspace/.cache/ms-playwright/chromium-1169/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
ERROR    [agent] ❌ Stopping due to 3 consecutive failures
WARNING  [agent] No history to create GIF from
WARNING  [agent] No history to create GIF from
