
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: organize_documentation_content.py
# execution: true
import json
import os
from datetime import datetime

print("Organizing all extracted information for comprehensive documentation...")
print("="*80)

# List all JSON files created during extraction
json_files = [
    "./basic_concepts_20250711-144313_content.json",
    "./command_reference_20250711-144724_content.json", 
    "./common_examples_20250711-144949_content.json",
    "./automated_trading_examples_20250711-145008_content.json",
    "./getting_alerts_from_tradingview_20250711-145029_content.json",
    "./api_keys_binance_spot_20250711-145349_content.json",
    "./api_keys_binance_futures_20250711-145410_content.json",
    "./api_keys_bybit_20250711-145430_content.json",
    "./api_keys_okx_20250711-145452_content.json"
]

# Check which files exist and organize content
existing_files = []
total_links = 0
total_images = 0

print("Available extracted content files:")
for file in json_files:
    if os.path.exists(file):
        existing_files.append(file)
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                links_count = len(data.get('links', []))
                images_count = len(data.get('images', []))
                total_links += links_count
                total_images += images_count
                print(f"✓ {file}: {links_count} links, {images_count} images")
        except Exception as e:
            print(f"✗ {file}: Error reading file - {e}")
    else:
        print(f"✗ {file}: File not found")

print(f"\nTotal extracted content: {total_links} links, {total_images} images")
print(f"Successfully extracted from {len(existing_files)} documentation pages")

# Create content structure for documentation
documentation_structure = {
    "timestamp": datetime.now().isoformat(),
    "source_website": "https://alertatron.com/docs/automated-trading/basic-concepts",
    "extraction_summary": {
        "total_pages_analyzed": len(existing_files),
        "total_links_extracted": total_links,
        "total_images_extracted": total_images,
        "extraction_files": existing_files
    },
    "content_categories": {
        "basic_concepts": "Platform overview and fundamental concepts",
        "command_reference": "Complete trading command syntax and parameters", 
        "examples": "Practical implementation examples and code samples",
        "api_integration": "Exchange API setup and integration procedures",
        "tradingview_integration": "TradingView webhook integration methods"
    },
    "supported_exchanges": [
        "Binance Spot", "Binance Futures", "Binance US", "Bybit", "OKX",
        "Bitfinex Spot and Margin", "BitMEX", "Bitget", "Deribit Futures"
    ],
    "order_types": [
        "Market Order", "Limit Order", "Stop Order", "Trailing Stop Order",
        "Trailing Take Profit Order", "Iceberg Order", "Grid Order", "Scaled Order",
        "TWAP Order", "Ping Pong Order", "Market Maker Order", "One Cancels Other",
        "Managed Order", "Aggressive Entry Order", "Dynamic Take Profit"
    ]
}

# Save organized content structure
with open("alertatron_documentation_structure.json", "w", encoding='utf-8') as f:
    json.dump(documentation_structure, f, indent=2, ensure_ascii=False)

print("="*80)
print("Content organization completed.")
print("Saved comprehensive structure to: alertatron_documentation_structure.json")
print("="*80)