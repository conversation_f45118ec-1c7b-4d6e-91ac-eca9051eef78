
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: examine_extraction_discrepancy.py
# execution: true
import json
import os

print("Examining extraction discrepancy...")
print("="*80)

# Check what was actually extracted
files_to_check = [
    "complete_website_extraction.json",
    "complete_extraction_summary.txt"
]

print("1. Checking extracted content files...")
for file in files_to_check:
    if os.path.exists(file):
        size = os.path.getsize(file)
        print(f"✅ {file}: {size:,} bytes")
    else:
        print(f"❌ {file}: NOT FOUND")

# Load and examine the extracted content
if os.path.exists("complete_website_extraction.json"):
    with open("complete_website_extraction.json", "r", encoding='utf-8') as f:
        extracted_data = json.load(f)
    
    print(f"\n2. Extracted data contains {len(extracted_data)} pages:")
    for page_name, content in extracted_data.items():
        print(f"   • {page_name}: {len(content['content'])} characters")
        print(f"     Title: {content['title'][:100]}...")
        print(f"     Content preview: {content['content'][:200]}...")
        print(f"     Code blocks: {len(content['code_blocks'])}")
        print()

# Compare with previous findings
print("\n3. DISCREPANCY ANALYSIS:")
print("="*50)
print("PREVIOUS FINDINGS (via ask_question_about_documents):")
previous_pages = [
    "basic-concepts", "command-reference", "common-examples",
    "automated-trading-examples", "getting-alerts-from-tradingview",
    "api-keys-binance-spot", "api-keys-binance-futures", 
    "api-keys-bybit", "api-keys-okx"
]

print(f"Previously found: {len(previous_pages)} pages")
for page in previous_pages:
    print(f"   • {page}")

print(f"\nDirect scraping found: {len(extracted_data)} pages")
for page_name in extracted_data.keys():
    print(f"   • {page_name}")

print("\n4. MISSING PAGES ANALYSIS:")
print("="*50)
missing_pages = []
for page in previous_pages:
    if page not in extracted_data:
        missing_pages.append(page)

print(f"Missing pages: {len(missing_pages)}")
for page in missing_pages:
    print(f"   ❌ {page}")
    # Try to construct the likely URL
    likely_url = f"https://alertatron.com/docs/automated-trading/{page}"
    print(f"      Likely URL: {likely_url}")

print("\n5. CONCLUSION:")
print("="*50)
if len(missing_pages) > 0:
    print("❌ CRITICAL ISSUE: Direct scraping missed many pages")
    print("❌ The scraping approach failed to find all documentation links")
    print("❌ This violates the 'tüm bilgiler' (all information) requirement")
    print("\n✅ SOLUTION: Use the previously extracted content which was more complete")
    print("✅ The ask_question_about_documents approach was more successful")
else:
    print("✅ All pages were successfully extracted")

print("="*80)