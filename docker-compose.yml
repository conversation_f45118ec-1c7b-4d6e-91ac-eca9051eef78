version: '3.8'

services:
  alertatron-ai-agent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - DATABASE_URL=**********************************************/alertatron
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    restart: unless-stopped
    networks:
      - alertatron-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=alertatron
      - POSTGRES_USER=alertatron
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - alertatron-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - alertatron-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - alertatron-ai-agent
    restart: unless-stopped
    networks:
      - alertatron-network

volumes:
  postgres_data:
  redis_data:

networks:
  alertatron-network:
    driver: bridge
