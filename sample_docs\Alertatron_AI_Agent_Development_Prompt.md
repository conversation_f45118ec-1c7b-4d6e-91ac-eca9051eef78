# Kapsamlı Bağlam Mühendisliği: Alertatron AI Agent Geliştirme Promptu

## 🎯 **ANA GÖREVİNİZ**

<PERSON><PERSON>, `Alertatron_AI_Agent_Project` klasöründeki kapsamlı dokümantasyonu analiz ederek, TradingView webhook'larını kullanarak çalışan profesyonel bir algoritmik ticaret sistemi geliştirecek uzman bir Python developerısınız. Bu sistem, 9 kripto para borsasında 18 farklı emir tipi ile otomatik ticaret yapabilmelidir.

## 📁 **KAYNAK DOKÜMANTASYON**

Aşağıdaki klasör yapısında bulunan tüm dosyaları detaylıca analiz edin:

```
Alertatron_AI_Agent_Project/
├── 01_Documentation/               # Ana PDF dokümantasyonlar
├── 02_Technical_Specifications/    # Teknik spesifikasyonlar
├── 03_Order_Types_Commands/        # 18 emir tipi detayları
├── 04_API_Integration/             # 9 borsa API rehberi
├── 05_Examples_and_Usage/          # Kullanım örnekleri
├── 06_Screenshots_and_Visuals/     # Görsel dokümantasyon
├── 07_Extracted_Data/              # Ham çıkarılan veriler
├── 08_Implementation_Guide/        # Python implementasyon rehberi
├── README.md                       # Proje açıklaması
└── project_summary.json           # Proje özeti
```

## 🔧 **TEKNİK REQUİREMENTS**

### **1. Webhook Integration Framework**
```python
# TradingView Webhook Format: MyKeys(SYMBOL) { command(parameters); } #bot
# Örnek: MyKeys(BTCUSDT) { market(side=buy, amount=50%); } #bot
```

**Gereksinimler:**
- Flask/FastAPI webhook endpoint'i
- TradingView mesaj parsing sistemi
- Alertatron komut format desteği
- Gerçek zamanlı sinyal işleme
- JSON webhook payload handling

### **2. Desteklenmesi Gereken 18 Emir Tipi**

**A. Temel Emirler:**
1. `market(side, amount, position, reduceOnly)` - Piyasa emri
2. `limit(side, amount, offset, postOnly, reduceOnly, position, tag)` - Limit emri
3. `stop(side, amount, offset, ...)` - Stop emri
4. `stopOrTakeProfit(...)` - Stop-loss/take-profit

**B. Takip Eden Emirler:**
5. `trailingStop(...)` - Takip eden stop
6. `trailingTakeProfit(...)` - Takip eden kar al
7. `trailingLimit(...)` - Takip eden limit

**C. Algoritmik Emirler:**
8. `iceberg(...)` - Buzdağı emri
9. `grid(...)` - Izgara emri
10. `scaled(...)` - Kademeli emir
11. `twap(...)` - Zaman ağırlıklı ortalama

**D. Gelişmiş Stratejiler:**
12. `pingPong(...)` - Ping pong emri
13. `marketMaker(...)` - Market maker
14. `aggressiveEntry(...)` - Agresif giriş
15. `dynamicTakeProfit(...)` - Dinamik kar al
16. `oco(...)` - One cancels other
17. `waitingLimit(...)` - Bekleyen limit
18. `managed(...)` - Yönetilen emir

### **3. Kripto Borsa Entegrasyonları**

**Desteklenmesi Gereken 9 Borsa:**
- Binance Spot, Binance Futures, Binance US
- Bybit (Perpetual/Spot)
- OKX, BitMEX, Bitfinex, Bitget, Deribit

**API Entegrasyon Gereksinimleri:**
- Her borsa için ayrı connector sınıfı
- Unified interface pattern
- API key yönetimi ve güvenlik
- Rate limiting ve error handling
- WebSocket real-time data streams

## 🏗️ **MİMARİ REQUİREMENTS**

### **Proje Yapısı:**
```python
alertatron_ai_agent/
├── main.py                     # Ana uygulama entry point
├── config/
│   ├── __init__.py
│   ├── settings.py            # Yapılandırma yönetimi
│   └── api_keys.py            # API anahtarı yönetimi
├── webhook/
│   ├── __init__.py
│   ├── handler.py             # TradingView webhook handler
│   ├── parser.py              # Alertatron komut parser
│   └── validator.py           # Gelen sinyal doğrulama
├── exchanges/
│   ├── __init__.py
│   ├── base_connector.py      # Temel borsa arayüzü
│   ├── binance_connector.py   # Binance entegrasyonu
│   ├── bybit_connector.py     # Bybit entegrasyonu
│   └── [diğer_borsalar].py
├── orders/
│   ├── __init__.py
│   ├── order_factory.py       # Emir factory pattern
│   ├── basic_orders.py        # Market, limit, stop
│   ├── trailing_orders.py     # Trailing emirler
│   ├── algorithmic_orders.py  # Grid, TWAP, iceberg
│   └── advanced_orders.py     # Ping pong, market maker
├── risk_management/
│   ├── __init__.py
│   ├── position_manager.py    # Pozisyon yönetimi
│   ├── risk_calculator.py     # Risk hesaplamaları
│   └── portfolio_manager.py   # Portföy yönetimi
├── utils/
│   ├── __init__.py
│   ├── logger.py              # Loglama sistemi
│   ├── database.py            # Veritabanı işlemleri
│   └── notifications.py       # Bildirim sistemi
└── tests/
    ├── __init__.py
    ├── test_webhook.py
    ├── test_orders.py
    └── test_exchanges.py
```

## 💼 **İMPLEMENTASYON TALİMATLARI**

### **ADIM 1: Proje Setup**
```python
# requirements.txt oluşturun:
# fastapi, uvicorn, ccxt, websockets, asyncio
# pandas, numpy, python-dotenv, pydantic
# sqlalchemy, alembic, redis, celery
```

### **ADIM 2: Webhook Handler Geliştirme**
```python
@app.post("/webhook")
async def handle_tradingview_webhook(webhook_data: dict):
    """
    TradingView webhook'larını işleyen ana endpoint
    Format: MyKeys(SYMBOL) { command(params); } #bot
    """
    # 1. Webhook mesajını parse et
    # 2. Alertatron komut formatını analiz et
    # 3. API key'i doğrula
    # 4. Emir tipini belirle
    # 5. İlgili borsaya yönlendir
    # 6. Risk kontrolü yap
    # 7. Emri execute et
    # 8. Sonucu logla
```

## 🛡️ **GÜVENLİK VE RİSK YÖNETİMİ**

### **Güvenlik Gereksinimleri:**
```python
class SecurityManager:
    def validate_api_keys(self, keys_name):
        # API anahtarlarını doğrula
    
    def check_trading_permissions(self, exchange, action):
        # İşlem izinlerini kontrol et
    
    def encrypt_sensitive_data(self, data):
        # Hassas verileri şifrele
```

## 📋 **DEVELOPMENT CHECKLIST**

### **Fase 1: Core Infrastructure**
- [ ] FastAPI webhook endpoint kurulumu
- [ ] Alertatron command parser
- [ ] Base exchange connector interface
- [ ] Temel emir tipleri (market, limit, stop)
- [ ] Configuration management
- [ ] Logging system

### **Fase 2: Exchange Integrations**
- [ ] Binance Spot/Futures connector
- [ ] Bybit connector
- [ ] OKX connector
- [ ] API key management
- [ ] Error handling & retry logic
- [ ] Rate limiting

### **Fase 3: Advanced Order Types**
- [ ] Trailing orders (stop, take profit, limit)
- [ ] Algorithmic orders (iceberg, grid, TWAP)
- [ ] Advanced strategies (ping pong, market maker)
- [ ] Dynamic take profit
- [ ] OCO orders

## 🎯 **BAŞARI KRİTERLERİ**

Geliştirdiğiniz sistem şu kriterleri karşılamalıdır:

1. **✅ Fonksiyonellik:** Tüm 18 emir tipi çalışır durumda
2. **✅ Güvenilirlik:** %99.9 uptime, hata handling
3. **✅ Güvenlik:** API key encryption, secure connections
4. **✅ Performans:** <100ms webhook response time
5. **✅ Ölçeklenebilirlik:** Concurrent webhook handling
6. **✅ Maintainability:** Clean code, documentation
7. **✅ Testability:** %80+ test coverage

---

**NOT:** Bu prompt'u kullanarak geliştireceğiniz sistem, gerçek para ile ticaret yapacaktır. Güvenlik, risk yönetimi ve test süreçlerine maximum önem verin. Küçük miktarlarla test yapmadan production'a geçmeyin.
