"""
Tests for exchange connector functionality.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from alertatron_ai_agent.exchanges.base_connector import BaseExchangeConnector, OrderSide, Order, Balance
from alertatron_ai_agent.exchanges.binance_connector import BinanceConnector
from alertatron_ai_agent.exchanges.exchange_manager import ExchangeManager
from alertatron_ai_agent.config.api_keys import ExchangeCredentials


class MockExchangeConnector(BaseExchangeConnector):
    """Mock exchange connector for testing."""
    
    def _initialize_exchange(self):
        """Mock initialization."""
        self.exchange = Mock()
    
    async def connect(self) -> bool:
        """Mock connect."""
        self.is_connected = True
        return True
    
    async def disconnect(self):
        """Mock disconnect."""
        self.is_connected = False
    
    async def get_balance(self):
        """Mock get balance."""
        return {
            "USDT": Balance(currency="USDT", free=1000.0, used=0.0, total=1000.0),
            "BTC": Balance(currency="BTC", free=0.1, used=0.0, total=0.1)
        }
    
    async def get_positions(self):
        """Mock get positions."""
        return []
    
    async def create_market_order(self, symbol, side, amount, **kwargs):
        """Mock create market order."""
        return Order(
            id="mock_order_123",
            symbol=symbol,
            type="market",
            side=side.value,
            amount=amount,
            status="filled",
            filled=amount,
            remaining=0.0,
            timestamp=1234567890
        )
    
    async def create_limit_order(self, symbol, side, amount, price, **kwargs):
        """Mock create limit order."""
        return Order(
            id="mock_limit_123",
            symbol=symbol,
            type="limit",
            side=side.value,
            amount=amount,
            price=price,
            status="open",
            filled=0.0,
            remaining=amount,
            timestamp=1234567890
        )
    
    async def create_stop_order(self, symbol, side, amount, stop_price, **kwargs):
        """Mock create stop order."""
        return Order(
            id="mock_stop_123",
            symbol=symbol,
            type="stop",
            side=side.value,
            amount=amount,
            price=stop_price,
            status="open",
            filled=0.0,
            remaining=amount,
            timestamp=1234567890
        )
    
    async def cancel_order(self, order_id, symbol):
        """Mock cancel order."""
        return True
    
    async def get_order_status(self, order_id, symbol):
        """Mock get order status."""
        return Order(
            id=order_id,
            symbol=symbol,
            type="limit",
            side="buy",
            amount=1.0,
            price=50000.0,
            status="open",
            filled=0.0,
            remaining=1.0,
            timestamp=1234567890
        )
    
    async def get_ticker(self, symbol):
        """Mock get ticker."""
        return {
            "symbol": symbol,
            "last": 50000.0,
            "bid": 49999.0,
            "ask": 50001.0,
            "high": 51000.0,
            "low": 49000.0,
            "volume": 1000.0
        }


class TestBaseExchangeConnector:
    """Test base exchange connector functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.credentials = ExchangeCredentials(
            api_key="test_key",
            secret="test_secret"
        )
        self.connector = MockExchangeConnector(self.credentials)
    
    @pytest.mark.asyncio
    async def test_connect(self):
        """Test connection functionality."""
        result = await self.connector.connect()
        assert result is True
        assert self.connector.is_connected is True
    
    @pytest.mark.asyncio
    async def test_disconnect(self):
        """Test disconnection functionality."""
        await self.connector.connect()
        await self.connector.disconnect()
        assert self.connector.is_connected is False
    
    @pytest.mark.asyncio
    async def test_get_balance(self):
        """Test getting balance."""
        await self.connector.connect()
        balances = await self.connector.get_balance()
        
        assert isinstance(balances, dict)
        assert "USDT" in balances
        assert isinstance(balances["USDT"], Balance)
        assert balances["USDT"].total == 1000.0
    
    @pytest.mark.asyncio
    async def test_create_market_order(self):
        """Test creating market order."""
        await self.connector.connect()
        
        order = await self.connector.create_market_order(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            amount=0.001
        )
        
        assert isinstance(order, Order)
        assert order.symbol == "BTCUSDT"
        assert order.side == "buy"
        assert order.amount == 0.001
        assert order.type == "market"
    
    @pytest.mark.asyncio
    async def test_create_limit_order(self):
        """Test creating limit order."""
        await self.connector.connect()
        
        order = await self.connector.create_limit_order(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            amount=0.001,
            price=49000.0
        )
        
        assert isinstance(order, Order)
        assert order.symbol == "BTCUSDT"
        assert order.side == "buy"
        assert order.amount == 0.001
        assert order.price == 49000.0
        assert order.type == "limit"
    
    @pytest.mark.asyncio
    async def test_get_ticker(self):
        """Test getting ticker information."""
        await self.connector.connect()
        
        ticker = await self.connector.get_ticker("BTCUSDT")
        
        assert isinstance(ticker, dict)
        assert "last" in ticker
        assert ticker["symbol"] == "BTCUSDT"
        assert ticker["last"] == 50000.0
    
    @pytest.mark.asyncio
    async def test_test_connection(self):
        """Test connection testing functionality."""
        result = await self.connector.test_connection()
        
        assert isinstance(result, dict)
        assert "connected" in result
        assert "exchange" in result
        assert result["connected"] is True


class TestBinanceConnector:
    """Test Binance connector functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.credentials = ExchangeCredentials(
            api_key="test_binance_key",
            secret="test_binance_secret",
            sandbox=True
        )
    
    @patch('alertatron_ai_agent.exchanges.binance_connector.ccxt')
    def test_initialize_exchange(self, mock_ccxt):
        """Test Binance exchange initialization."""
        mock_exchange = Mock()
        mock_ccxt.binance.return_value = mock_exchange
        
        connector = BinanceConnector(self.credentials)
        
        assert connector.exchange == mock_exchange
        mock_ccxt.binance.assert_called_once()
    
    def test_exchange_name(self):
        """Test exchange name property."""
        with patch('alertatron_ai_agent.exchanges.binance_connector.ccxt'):
            connector = BinanceConnector(self.credentials)
            assert "binance" in connector.exchange_name.lower()


class TestExchangeManager:
    """Test exchange manager functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        # We'll mock the API key manager to avoid dependency issues
        pass
    
    @patch('alertatron_ai_agent.exchanges.exchange_manager.get_api_key_manager')
    def test_exchange_manager_initialization(self, mock_api_key_manager):
        """Test exchange manager initialization."""
        mock_api_key_manager.return_value.list_available_keys.return_value = {}
        
        manager = ExchangeManager()
        
        assert isinstance(manager.exchange_classes, dict)
        assert "binance" in manager.exchange_classes
        assert isinstance(manager.connections, dict)
    
    def test_get_connector_not_found(self):
        """Test getting non-existent connector."""
        with patch('alertatron_ai_agent.exchanges.exchange_manager.get_api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.return_value.list_available_keys.return_value = {}
            
            manager = ExchangeManager()
            connector = manager.get_connector("nonexistent", "nonexistent")
            
            assert connector is None
    
    def test_find_connector_for_key_not_found(self):
        """Test finding connector for non-existent key."""
        with patch('alertatron_ai_agent.exchanges.exchange_manager.get_api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.return_value.list_available_keys.return_value = {}
            
            manager = ExchangeManager()
            connector = manager.find_connector_for_key("nonexistent")
            
            assert connector is None
    
    def test_get_connection_status(self):
        """Test getting connection status."""
        with patch('alertatron_ai_agent.exchanges.exchange_manager.get_api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.return_value.list_available_keys.return_value = {}
            
            manager = ExchangeManager()
            status = manager.get_connection_status()
            
            assert isinstance(status, dict)
            assert "total_exchanges" in status
            assert "total_connections" in status
            assert "exchanges" in status


@pytest.fixture
def mock_credentials():
    """Mock credentials for testing."""
    return ExchangeCredentials(
        api_key="test_key",
        secret="test_secret",
        sandbox=True
    )


@pytest.fixture
def mock_exchange_config():
    """Mock exchange configuration."""
    return {
        "enableRateLimit": True,
        "rateLimit": 1200,
        "sandbox": True
    }


class TestExchangeIntegration:
    """Integration tests for exchange functionality."""
    
    @pytest.mark.asyncio
    async def test_full_order_flow(self, mock_credentials):
        """Test complete order flow through exchange connector."""
        connector = MockExchangeConnector(mock_credentials)
        
        # Connect
        connected = await connector.connect()
        assert connected is True
        
        # Get balance
        balances = await connector.get_balance()
        assert len(balances) > 0
        
        # Create market order
        order = await connector.create_market_order(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            amount=0.001
        )
        assert order.status == "filled"
        
        # Get ticker
        ticker = await connector.get_ticker("BTCUSDT")
        assert ticker["last"] > 0
        
        # Disconnect
        await connector.disconnect()
        assert connector.is_connected is False
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mock_credentials):
        """Test error handling in exchange operations."""
        connector = MockExchangeConnector(mock_credentials)
        
        # Test operation without connection
        with pytest.raises(Exception):
            # This should raise an exception in a real implementation
            pass
    
    def test_order_conversion(self, mock_credentials):
        """Test CCXT order conversion."""
        connector = MockExchangeConnector(mock_credentials)
        
        ccxt_order = {
            "id": "test_123",
            "symbol": "BTCUSDT",
            "type": "market",
            "side": "buy",
            "amount": 0.001,
            "price": None,
            "status": "filled",
            "filled": 0.001,
            "remaining": 0.0,
            "timestamp": 1234567890
        }
        
        order = connector._convert_ccxt_order(ccxt_order)
        
        assert isinstance(order, Order)
        assert order.id == "test_123"
        assert order.symbol == "BTCUSDT"
        assert order.amount == 0.001
