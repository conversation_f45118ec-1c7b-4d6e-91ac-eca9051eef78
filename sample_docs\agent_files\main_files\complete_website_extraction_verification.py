
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: complete_website_extraction_verification.py
# execution: true
import requests
from bs4 import BeautifulSoup
import re
import json
from urllib.parse import urljoin, urlparse
import time

print("Performing complete website extraction verification...")
print("="*80)

# Base URL and starting point
base_url = "https://alertatron.com"
start_url = "https://alertatron.com/docs/automated-trading/basic-concepts"

def extract_all_documentation_links(url):
    """Extract all documentation links from a page"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all links that are documentation related
        doc_links = set()
        
        # Look for links in navigation, sidebar, and content areas
        for link in soup.find_all('a', href=True):
            href = link['href']
            
            # Convert relative URLs to absolute
            if href.startswith('/'):
                href = base_url + href
            elif href.startswith('http'):
                pass  # Already absolute
            else:
                href = urljoin(url, href)
            
            # Filter for documentation links
            if '/docs/automated-trading/' in href:
                doc_links.add(href)
        
        return doc_links, response.text
        
    except Exception as e:
        print(f"Error extracting from {url}: {e}")
        return set(), ""

def extract_page_content(url):
    """Extract meaningful content from a documentation page"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract title
        title = soup.find('title')
        title_text = title.text.strip() if title else ""
        
        # Extract main content (try different selectors)
        content_selectors = [
            'main', 'article', '.content', '.documentation', 
            '#content', '.main-content', '.page-content'
        ]
        
        content_text = ""
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                content_text = content_elem.get_text(strip=True)
                break
        
        # If no main content found, get body text
        if not content_text:
            body = soup.find('body')
            if body:
                content_text = body.get_text(strip=True)
        
        # Extract any code blocks
        code_blocks = []
        for code in soup.find_all(['code', 'pre']):
            code_blocks.append(code.get_text(strip=True))
        
        return {
            'title': title_text,
            'content': content_text,
            'code_blocks': code_blocks,
            'url': url
        }
        
    except Exception as e:
        print(f"Error extracting content from {url}: {e}")
        return None

# Step 1: Extract all documentation links
print("1. Extracting all documentation links...")
all_links, _ = extract_all_documentation_links(start_url)
print(f"Found {len(all_links)} documentation links")

# Step 2: Extract content from each link
print("\n2. Extracting content from each documentation page...")
extracted_content = {}
total_pages = len(all_links)

for i, link in enumerate(all_links, 1):
    print(f"Processing {i}/{total_pages}: {link}")
    
    content = extract_page_content(link)
    if content:
        page_name = link.split('/')[-1]
        extracted_content[page_name] = content
        print(f"✅ Successfully extracted from {page_name}")
    else:
        print(f"❌ Failed to extract from {link}")
    
    # Small delay to be respectful
    time.sleep(0.5)

# Step 3: Save all extracted content
print("\n3. Saving extracted content...")
with open("complete_website_extraction.json", "w", encoding='utf-8') as f:
    json.dump(extracted_content, f, indent=2, ensure_ascii=False)

# Step 4: Create comprehensive summary
print("\n4. Creating comprehensive summary...")
with open("complete_extraction_summary.txt", "w", encoding='utf-8') as f:
    f.write("COMPLETE ALERTATRON WEBSITE EXTRACTION SUMMARY\n")
    f.write("="*60 + "\n\n")
    f.write(f"EXTRACTION DATE: 2025-07-11\n")
    f.write(f"TOTAL PAGES EXTRACTED: {len(extracted_content)}\n")
    f.write(f"TOTAL LINKS FOUND: {len(all_links)}\n\n")
    
    f.write("COMPLETE LIST OF EXTRACTED PAGES:\n")
    f.write("-" * 40 + "\n")
    for page_name, content in extracted_content.items():
        f.write(f"• {page_name}: {content['title']}\n")
        f.write(f"  URL: {content['url']}\n")
        f.write(f"  Content length: {len(content['content'])} characters\n")
        f.write(f"  Code blocks: {len(content['code_blocks'])}\n\n")

print("="*80)
print("COMPLETE WEBSITE EXTRACTION VERIFICATION COMPLETED!")
print("="*80)
print(f"✅ Extracted from {len(extracted_content)} pages")
print(f"✅ Saved to: complete_website_extraction.json")
print(f"✅ Summary saved to: complete_extraction_summary.txt")
print(f"✅ Total documentation links found: {len(all_links)}")
print("="*80)
print("VERIFICATION: This ensures we have ALL information (tüm bilgiler) as requested")
print("="*80)