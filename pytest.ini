[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=alertatron_ai_agent
    --cov-report=html
    --cov-report=term-missing
markers =
    asyncio: marks tests as async
    integration: marks tests as integration tests
    slow: marks tests as slow running
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
