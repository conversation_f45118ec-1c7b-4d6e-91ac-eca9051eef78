"""
Bybit exchange connector implementation.
"""

from typing import Dict, Any, List, Optional
import ccxt.async_support as ccxt
import asyncio

from .base_connector import BaseExchangeConnector, Order, Balance, Position, OrderSide
from ..utils.logger import get_logger
from ..config.api_keys import ExchangeCredentials


logger = get_logger("bybit_connector")


class BybitConnector(BaseExchangeConnector):
    """Bybit exchange connector."""
    
    def _initialize_exchange(self):
        """Initialize the Bybit CCXT exchange instance."""
        try:
            self.exchange = ccxt.bybit({
                'apiKey': self.credentials.api_key,
                'secret': self.credentials.secret,
                'sandbox': self.credentials.sandbox,
                'enableRateLimit': self.config.get('enableRateLimit', True),
                'rateLimit': self.config.get('rateLimit', 120),
                'options': {
                    'defaultType': 'swap',  # swap, spot, future, option
                }
            })
            
            logger.info("Bybit exchange initialized", 
                       sandbox=self.credentials.sandbox)
        
        except Exception as e:
            logger.error("Failed to initialize Bybit exchange", error=str(e))
            raise
    
    async def connect(self) -> bool:
        """Connect to Bybit and validate credentials."""
        try:
            if not self.exchange:
                self._initialize_exchange()
            
            # Test connection by fetching account info
            await self.exchange.fetch_balance()
            self.is_connected = True
            
            logger.info("Connected to Bybit successfully")
            return True
        
        except Exception as e:
            self.is_connected = False
            self._handle_exchange_error(e, "connect")
            return False
    
    async def disconnect(self):
        """Disconnect from Bybit."""
        try:
            if self.exchange:
                await self.exchange.close()
            self.is_connected = False
            logger.info("Disconnected from Bybit")
        
        except Exception as e:
            logger.error("Error disconnecting from Bybit", error=str(e))
    
    async def get_balance(self) -> Dict[str, Balance]:
        """Get Bybit account balance."""
        try:
            if not self.is_connected:
                await self.connect()
            
            balance_data = await self.exchange.fetch_balance()
            balances = {}
            
            for currency, balance_info in balance_data.items():
                if currency not in ['info', 'free', 'used', 'total']:
                    balances[currency] = Balance(
                        currency=currency,
                        free=float(balance_info.get('free', 0)),
                        used=float(balance_info.get('used', 0)),
                        total=float(balance_info.get('total', 0))
                    )
            
            return balances
        
        except Exception as e:
            self._handle_exchange_error(e, "get_balance")
    
    async def get_positions(self) -> List[Position]:
        """Get open positions from Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            positions_data = await self.exchange.fetch_positions()
            positions = []
            
            for pos_data in positions_data:
                if float(pos_data.get('contracts', 0)) != 0:  # Only non-zero positions
                    positions.append(Position(
                        symbol=pos_data.get('symbol', ''),
                        side=pos_data.get('side', ''),
                        size=float(pos_data.get('contracts', 0)),
                        entry_price=float(pos_data.get('entryPrice', 0)),
                        mark_price=float(pos_data.get('markPrice', 0)),
                        unrealized_pnl=float(pos_data.get('unrealizedPnl', 0)),
                        percentage=float(pos_data.get('percentage', 0))
                    ))
            
            return positions
        
        except Exception as e:
            self._handle_exchange_error(e, "get_positions")
    
    async def create_market_order(self, 
                                symbol: str, 
                                side: OrderSide, 
                                amount: float,
                                **kwargs) -> Order:
        """Create a market order on Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            order_result = await self.exchange.create_market_order(
                symbol=symbol,
                side=side.value,
                amount=amount,
                **kwargs
            )
            
            order = self._convert_ccxt_order(order_result)
            
            logger.info("Market order created",
                       symbol=symbol,
                       side=side.value,
                       amount=amount,
                       order_id=order.id)
            
            return order
        
        except Exception as e:
            self._handle_exchange_error(e, "create_market_order")
    
    async def create_limit_order(self, 
                               symbol: str, 
                               side: OrderSide, 
                               amount: float, 
                               price: float,
                               **kwargs) -> Order:
        """Create a limit order on Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            order_result = await self.exchange.create_limit_order(
                symbol=symbol,
                side=side.value,
                amount=amount,
                price=price,
                **kwargs
            )
            
            order = self._convert_ccxt_order(order_result)
            
            logger.info("Limit order created",
                       symbol=symbol,
                       side=side.value,
                       amount=amount,
                       price=price,
                       order_id=order.id)
            
            return order
        
        except Exception as e:
            self._handle_exchange_error(e, "create_limit_order")
    
    async def create_stop_order(self, 
                              symbol: str, 
                              side: OrderSide, 
                              amount: float, 
                              stop_price: float,
                              **kwargs) -> Order:
        """Create a stop order on Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            # Bybit uses conditional orders for stop orders
            order_result = await self.exchange.create_order(
                symbol=symbol,
                type='market',
                side=side.value,
                amount=amount,
                price=None,
                params={
                    'stopPx': stop_price,
                    'ordType': 'Stop',
                    **kwargs
                }
            )
            
            order = self._convert_ccxt_order(order_result)
            
            logger.info("Stop order created",
                       symbol=symbol,
                       side=side.value,
                       amount=amount,
                       stop_price=stop_price,
                       order_id=order.id)
            
            return order
        
        except Exception as e:
            self._handle_exchange_error(e, "create_stop_order")
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order on Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            await self.exchange.cancel_order(order_id, symbol)
            
            logger.info("Order canceled",
                       order_id=order_id,
                       symbol=symbol)
            
            return True
        
        except Exception as e:
            self._handle_exchange_error(e, "cancel_order")
            return False
    
    async def get_order_status(self, order_id: str, symbol: str) -> Order:
        """Get order status from Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            order_result = await self.exchange.fetch_order(order_id, symbol)
            return self._convert_ccxt_order(order_result)
        
        except Exception as e:
            self._handle_exchange_error(e, "get_order_status")
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get ticker information from Bybit."""
        try:
            if not self.is_connected:
                await self.connect()
            
            ticker = await self.exchange.fetch_ticker(symbol)
            return ticker
        
        except Exception as e:
            self._handle_exchange_error(e, "get_ticker")
