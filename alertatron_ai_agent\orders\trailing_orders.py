"""
Trailing order implementations: trailing<PERSON>top, trailingTakeProfit, trailingLimit.
"""

from typing import Dict, Any, Optional
import asyncio
from datetime import datetime, timedelta

from .order_factory import OrderExecutor
from ..webhook.parser import ParsedCommand
from ..exchanges.base_connector import OrderSide
from ..utils.logger import log_trade_execution


class TrailingStopExecutor(OrderExecutor):
    """Executor for trailing stop orders."""
    
    def __init__(self, connector):
        super().__init__(connector)
        self.active_trailing_orders = {}
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a trailing stop order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'offset'],
                optional=['amount', 'callbackRate', 'activationPrice', 'reduceOnly']
            )
            
            side = params['side']
            offset = params['offset']
            amount = params.get('amount')
            callback_rate = params.get('callbackRate', offset)  # Use offset as callback rate if not specified
            
            if not amount:
                raise ValueError("Amount is required for trailing stop orders")
            
            # Get current price
            current_price = await self.get_current_price(parsed_command.symbol)
            
            # Calculate actual amount
            actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
            
            # Calculate activation price if not provided
            activation_price = params.get('activationPrice')
            if not activation_price:
                activation_price = self.calculate_price_from_offset(current_price, offset, side)
            
            # Create trailing stop order
            order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
            
            # Try to use exchange native trailing stop if available
            try:
                order = await self._create_native_trailing_stop(
                    parsed_command.symbol, order_side, actual_amount, 
                    activation_price, callback_rate, params
                )
            except:
                # Fallback to manual trailing stop implementation
                order = await self._create_manual_trailing_stop(
                    parsed_command.symbol, order_side, actual_amount,
                    activation_price, callback_rate, params
                )
            
            # Log trade execution
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='trailing_stop',
                side=side,
                amount=actual_amount,
                price=activation_price,
                order_id=order.id if hasattr(order, 'id') else 'manual',
                status='placed'
            )
            
            return {
                "status": "success",
                "order_type": "trailing_stop",
                "order": order.dict() if hasattr(order, 'dict') else order,
                "activation_price": activation_price,
                "callback_rate": callback_rate,
                "current_price": current_price,
                "message": "Trailing stop order placed successfully"
            }
        
        except Exception as e:
            self.logger.error("Trailing stop order execution failed", error=str(e))
            raise
    
    async def _create_native_trailing_stop(self, symbol, side, amount, activation_price, callback_rate, params):
        """Create native trailing stop order if exchange supports it."""
        # This would be exchange-specific implementation
        # For now, raise exception to fallback to manual implementation
        raise NotImplementedError("Native trailing stop not implemented")
    
    async def _create_manual_trailing_stop(self, symbol, side, amount, activation_price, callback_rate, params):
        """Create manual trailing stop order using price monitoring."""
        trailing_order_id = f"trailing_{symbol}_{datetime.now().timestamp()}"
        
        trailing_order = {
            "id": trailing_order_id,
            "symbol": symbol,
            "side": side,
            "amount": amount,
            "activation_price": activation_price,
            "callback_rate": callback_rate,
            "best_price": None,
            "stop_price": None,
            "status": "monitoring",
            "created_at": datetime.now()
        }
        
        # Store for monitoring
        self.active_trailing_orders[trailing_order_id] = trailing_order
        
        # Start monitoring task
        asyncio.create_task(self._monitor_trailing_stop(trailing_order_id))
        
        return trailing_order
    
    async def _monitor_trailing_stop(self, order_id: str):
        """Monitor trailing stop order and adjust stop price."""
        try:
            order = self.active_trailing_orders.get(order_id)
            if not order:
                return
            
            while order["status"] == "monitoring":
                current_price = await self.get_current_price(order["symbol"])
                
                # Check if activation price is reached
                if order["best_price"] is None:
                    if ((order["side"] == OrderSide.SELL and current_price >= order["activation_price"]) or
                        (order["side"] == OrderSide.BUY and current_price <= order["activation_price"])):
                        order["best_price"] = current_price
                        order["status"] = "active"
                        self.logger.info("Trailing stop activated", order_id=order_id, price=current_price)
                
                # Update trailing stop if active
                if order["status"] == "active":
                    if order["side"] == OrderSide.SELL:
                        # For sell orders, trail upwards
                        if current_price > order["best_price"]:
                            order["best_price"] = current_price
                            callback_amount = float(order["callback_rate"].replace('%', '')) / 100 if '%' in str(order["callback_rate"]) else order["callback_rate"]
                            order["stop_price"] = current_price * (1 - callback_amount)
                        
                        # Check if stop should trigger
                        if order["stop_price"] and current_price <= order["stop_price"]:
                            await self._execute_trailing_stop(order_id)
                            break
                    
                    else:
                        # For buy orders, trail downwards
                        if current_price < order["best_price"]:
                            order["best_price"] = current_price
                            callback_amount = float(order["callback_rate"].replace('%', '')) / 100 if '%' in str(order["callback_rate"]) else order["callback_rate"]
                            order["stop_price"] = current_price * (1 + callback_amount)
                        
                        # Check if stop should trigger
                        if order["stop_price"] and current_price >= order["stop_price"]:
                            await self._execute_trailing_stop(order_id)
                            break
                
                # Wait before next check
                await asyncio.sleep(1)  # Check every second
        
        except Exception as e:
            self.logger.error("Trailing stop monitoring failed", order_id=order_id, error=str(e))
    
    async def _execute_trailing_stop(self, order_id: str):
        """Execute the trailing stop as market order."""
        try:
            order = self.active_trailing_orders.get(order_id)
            if not order:
                return
            
            # Execute market order
            market_order = await self.connector.create_market_order(
                symbol=order["symbol"],
                side=order["side"],
                amount=order["amount"]
            )
            
            order["status"] = "executed"
            order["executed_order"] = market_order
            
            self.logger.info("Trailing stop executed", 
                           order_id=order_id, 
                           market_order_id=market_order.id,
                           stop_price=order["stop_price"])
        
        except Exception as e:
            self.logger.error("Trailing stop execution failed", order_id=order_id, error=str(e))


class TrailingTakeProfitExecutor(OrderExecutor):
    """Executor for trailing take profit orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a trailing take profit order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'offset'],
                optional=['amount', 'callbackRate', 'activationPrice']
            )
            
            # Similar implementation to trailing stop but with profit logic
            side = params['side']
            offset = params['offset']
            amount = params.get('amount')
            
            if not amount:
                raise ValueError("Amount is required for trailing take profit orders")
            
            current_price = await self.get_current_price(parsed_command.symbol)
            actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
            
            # For take profit, we reverse the side logic
            profit_side = 'sell' if side.lower() == 'buy' else 'buy'
            activation_price = self.calculate_price_from_offset(current_price, offset, profit_side)
            
            # Create manual trailing take profit
            trailing_order_id = f"trailing_tp_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            trailing_order = {
                "id": trailing_order_id,
                "symbol": parsed_command.symbol,
                "side": OrderSide.SELL if profit_side == 'sell' else OrderSide.BUY,
                "amount": actual_amount,
                "activation_price": activation_price,
                "callback_rate": params.get('callbackRate', offset),
                "best_price": None,
                "take_profit_price": None,
                "status": "monitoring",
                "created_at": datetime.now()
            }
            
            # Start monitoring
            asyncio.create_task(self._monitor_trailing_take_profit(trailing_order_id, trailing_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='trailing_take_profit',
                side=profit_side,
                amount=actual_amount,
                price=activation_price,
                order_id=trailing_order_id,
                status='monitoring'
            )
            
            return {
                "status": "success",
                "order_type": "trailing_take_profit",
                "order": trailing_order,
                "activation_price": activation_price,
                "current_price": current_price,
                "message": "Trailing take profit order placed successfully"
            }
        
        except Exception as e:
            self.logger.error("Trailing take profit order execution failed", error=str(e))
            raise
    
    async def _monitor_trailing_take_profit(self, order_id: str, order: Dict[str, Any]):
        """Monitor trailing take profit order."""
        # Similar to trailing stop but with profit logic
        # Implementation would be similar to _monitor_trailing_stop
        pass


class TrailingLimitExecutor(OrderExecutor):
    """Executor for trailing limit orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a trailing limit order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'offset'],
                optional=['amount', 'trailAmount', 'limitOffset']
            )
            
            side = params['side']
            offset = params['offset']
            amount = params.get('amount')
            
            if not amount:
                raise ValueError("Amount is required for trailing limit orders")
            
            current_price = await self.get_current_price(parsed_command.symbol)
            actual_amount = self.calculate_amount_from_specification(amount, parsed_command.symbol, current_price)
            
            # Calculate initial limit price
            limit_price = self.calculate_price_from_offset(current_price, offset, side)
            
            # Create trailing limit order
            trailing_order_id = f"trailing_limit_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            trailing_order = {
                "id": trailing_order_id,
                "symbol": parsed_command.symbol,
                "side": OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL,
                "amount": actual_amount,
                "initial_limit_price": limit_price,
                "current_limit_price": limit_price,
                "trail_amount": params.get('trailAmount', offset),
                "status": "monitoring",
                "created_at": datetime.now()
            }
            
            # Start monitoring
            asyncio.create_task(self._monitor_trailing_limit(trailing_order_id, trailing_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='trailing_limit',
                side=side,
                amount=actual_amount,
                price=limit_price,
                order_id=trailing_order_id,
                status='monitoring'
            )
            
            return {
                "status": "success",
                "order_type": "trailing_limit",
                "order": trailing_order,
                "initial_limit_price": limit_price,
                "current_price": current_price,
                "message": "Trailing limit order placed successfully"
            }
        
        except Exception as e:
            self.logger.error("Trailing limit order execution failed", error=str(e))
            raise
    
    async def _monitor_trailing_limit(self, order_id: str, order: Dict[str, Any]):
        """Monitor trailing limit order and adjust limit price."""
        # Implementation for trailing limit logic
        pass
