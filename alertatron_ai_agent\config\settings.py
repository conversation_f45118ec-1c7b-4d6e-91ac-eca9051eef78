"""
Configuration settings for Alertatron AI Agent.
"""

from typing import Dict, List, Optional
from pydantic import BaseSettings, <PERSON>
from pydantic_settings import BaseSettings as PydanticBaseSettings
import os


class Settings(PydanticBaseSettings):
    """Main application settings."""
    
    # Application settings
    app_name: str = "Alertatron AI Agent"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # Security settings
    secret_key: str = Field(env="SECRET_KEY")
    webhook_secret: Optional[str] = Field(default=None, env="WEBHOOK_SECRET")
    
    # Database settings
    database_url: str = Field(env="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Trading settings
    max_concurrent_orders: int = Field(default=100, env="MAX_CONCURRENT_ORDERS")
    default_timeout: int = Field(default=30, env="DEFAULT_TIMEOUT")
    
    # Risk management settings
    max_position_size: float = Field(default=10000.0, env="MAX_POSITION_SIZE")
    max_daily_loss: float = Field(default=1000.0, env="MAX_DAILY_LOSS")
    
    # Supported exchanges
    supported_exchanges: List[str] = [
        "binance",
        "binanceusdm",  # Binance Futures
        "binanceus",
        "bybit",
        "okx",
        "bitmex",
        "bitfinex",
        "bitget",
        "deribit"
    ]
    
    # Exchange-specific settings
    exchange_settings: Dict[str, Dict] = {
        "binance": {
            "sandbox": False,
            "rateLimit": 1200,
            "enableRateLimit": True
        },
        "binanceusdm": {
            "sandbox": False,
            "rateLimit": 1200,
            "enableRateLimit": True
        },
        "bybit": {
            "sandbox": False,
            "rateLimit": 120,
            "enableRateLimit": True
        },
        "okx": {
            "sandbox": False,
            "rateLimit": 100,
            "enableRateLimit": True
        }
    }
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


def get_exchange_config(exchange_name: str) -> Dict:
    """Get configuration for a specific exchange."""
    return settings.exchange_settings.get(exchange_name, {})
