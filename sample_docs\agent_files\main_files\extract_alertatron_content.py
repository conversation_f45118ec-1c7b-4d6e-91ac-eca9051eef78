
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: extract_alertatron_content.py
# execution: true
from api_server.agent_tools.ask_question_about_documents import ask_question_about_documents

# Extract comprehensive information from the Alertatron documentation
url = "https://alertatron.com/docs/automated-trading/basic-concepts"

# First, get an overview of all available content
query = """
Please provide a comprehensive overview of ALL information available on this page about automated trading. 
I need to understand:
1. What is Alertatron and its core capabilities
2. All basic concepts related to automated trading
3. TradingView webhook integration details
4. Technical implementation information
5. API documentation and endpoints
6. Configuration and setup procedures
7. Code examples and implementation guides
8. Any architecture diagrams or technical illustrations
9. Security and authentication methods
10. Error handling and troubleshooting information

Please extract ALL information systematically, as this will be used to create comprehensive documentation for an algorithmic trading project.
"""

print("Extracting comprehensive information from Alertatron documentation...")
print("="*70)

csv_answer, json_answer, rag_answer, advanced_rag_answers = ask_question_about_documents(
    query=query, 
    urls=[url]
)

print("="*70)
print("Content extraction completed. Now analyzing the extracted information...")