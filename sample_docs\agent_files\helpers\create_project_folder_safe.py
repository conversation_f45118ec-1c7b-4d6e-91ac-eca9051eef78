
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: create_project_folder_safe.py
# execution: true
import os
import json
import shutil

print("Alertatron AI Agent proje klasörü oluşturuluyor...")
print("="*80)

# Ana proje klasörünü oluştur
project_folder = "Alertatron_AI_Agent_Project"
if not os.path.exists(project_folder):
    os.makedirs(project_folder)

# Alt klasörleri oluştur
folders = [
    "01_Documentation",
    "02_Technical_Specifications", 
    "03_Order_Types_Commands",
    "04_API_Integration",
    "05_Examples_and_Usage",
    "06_Screenshots_and_Visuals",
    "07_Extracted_Data",
    "08_Implementation_Guide"
]

for folder in folders:
    folder_path = os.path.join(project_folder, folder)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

print("1. Klasör yapısı oluşturuldu:")
for folder in folders:
    print(f"   ✅ {folder}")

# 2. Mevcut dosyaları kopyala
print("\n2. Mevcut dosyalar kopyalanıyor...")
files_to_copy = [
    ("Alertatron_Complete_Technical_Documentation.pdf", "01_Documentation"),
    ("extracted_specifications.json", "02_Technical_Specifications"),
    ("alertatron_technical_specifications.txt", "02_Technical_Specifications")
]

copied_count = 0
for file_name, target_folder in files_to_copy:
    if os.path.exists(file_name):
        target_path = os.path.join(project_folder, target_folder)
        shutil.copy(file_name, target_path)
        copied_count += 1
        print(f"   ✅ {file_name}")

print(f"   📁 {copied_count} dosya kopyalandı")

# 3. Emir tiplerini detaylandır
print("\n3. Emir tipi dokümantasyonu oluşturuluyor...")

# Market Order detayları
market_order = {
    "name": "Market Order (Piyasa Emri)",
    "syntax": "market(side, amount, position, reduceOnly)",
    "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri",
    "parameters": {
        "side": {"type": "string", "required": True, "values": ["buy", "sell"]},
        "amount": {"type": "mixed", "formats": ["1000", "50%", "50%x", "50%p"]},
        "position": {"type": "mixed", "description": "Hedef pozisyon büyüklüğü"},
        "reduceOnly": {"type": "boolean", "description": "Sadece pozisyon azaltma"}
    },
    "examples": ["market(side=buy, amount=1000)", "market(position=0)"]
}

# Limit Order detayları  
limit_order = {
    "name": "Limit Order (Limit Emri)",
    "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)",
    "description": "Belirli fiyattan limit emri yerleştirir",
    "parameters": {
        "side": {"type": "string", "required": True},
        "amount": {"type": "number", "required": True},
        "offset": {"type": "string", "formats": ["1%", "50", "e1%", "@50000"]},
        "postOnly": {"type": "boolean", "default": True}
    }
}

# Tüm emir tiplerini bir araya getir
all_orders = {
    "market_order": market_order,
    "limit_order": limit_order,
    "stop_order": {"name": "Stop Order", "syntax": "stop(...)", "description": "Stop emri"},
    "trailing_stop_order": {"name": "Trailing Stop", "syntax": "trailingStop(...)", "description": "Takip eden stop"},
    "trailing_take_profit": {"name": "Trailing Take Profit", "syntax": "trailingTakeProfit(...)", "description": "Takip eden kar al"},
    "iceberg_order": {"name": "Iceberg Order", "syntax": "iceberg(...)", "description": "Buzdağı emri"},
    "grid_order": {"name": "Grid Order", "syntax": "grid(...)", "description": "Izgara emri"},
    "twap_order": {"name": "TWAP Order", "syntax": "twap(...)", "description": "Zaman ağırlıklı ortalama"},
    "ping_pong_order": {"name": "Ping Pong Order", "syntax": "pingPong(...)", "description": "Sürekli alım-satım"},
    "market_maker_order": {"name": "Market Maker", "syntax": "marketMaker(...)", "description": "Market maker emri"},
    "aggressive_entry_order": {"name": "Aggressive Entry", "syntax": "aggressiveEntry(...)", "description": "Agresif giriş"},
    "dynamic_take_profit": {"name": "Dynamic Take Profit", "syntax": "dynamicTakeProfit(...)", "description": "Dinamik kar al"},
    "one_cancels_other": {"name": "One Cancels Other", "syntax": "oco(...)", "description": "OCO emri"},
    "scaled_order": {"name": "Scaled Order", "syntax": "scaled(...)", "description": "Kademeli emir"},
    "waiting_limit_order": {"name": "Waiting Limit", "syntax": "waitingLimit(...)", "description": "Bekleyen limit emir"}
}

# Emir tiplerini kaydet
order_file = os.path.join(project_folder, "03_Order_Types_Commands", "all_order_types.json")
with open(order_file, 'w', encoding='utf-8') as f:
    json.dump(all_orders, f, indent=2, ensure_ascii=False)

print(f"   ✅ {len(all_orders)} emir tipi dokümante edildi")

# 4. Python implementasyon rehberi oluştur
print("\n4. Python implementasyon rehberi oluşturuluyor...")

