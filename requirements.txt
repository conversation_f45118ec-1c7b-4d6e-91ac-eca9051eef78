# Core FastAPI and web framework dependencies
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0
python-multipart>=0.0.6

# Cryptocurrency exchange libraries
ccxt>=4.0.0
websockets>=11.0

# Async and concurrency
aiohttp>=3.8.0
aiofiles>=23.0.0

# Data processing and analysis
numpy>=1.24.0
python-dateutil>=2.8.0

# Configuration and environment
python-dotenv>=1.0.0
pydantic-settings>=2.0.0

# Security and encryption
cryptography>=40.0.0

# Logging and monitoring
structlog>=23.0.0
rich>=13.0.0

# Testing
pytest>=7.0.0
pytest-asyncio>=0.20.0
httpx>=0.24.0
