# Core FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# Cryptocurrency exchange libraries
ccxt==4.1.64
websockets==12.0

# Async and concurrency
asyncio-mqtt==0.16.1
aiohttp==3.9.1
aiofiles==23.2.0

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
python-dateutil==2.8.2

# Configuration and environment
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Database and caching
sqlalchemy==2.0.23
alembic==1.13.1
redis==5.0.1
asyncpg==0.29.0

# Task queue and background jobs
celery==5.3.4
kombu==5.3.4

# Security and encryption
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Logging and monitoring
structlog==23.2.0
rich==13.7.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
