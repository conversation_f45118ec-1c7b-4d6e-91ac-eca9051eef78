{"supported_exchanges": [{"name": "Binance Spot", "features": ["spot trading"], "notes": "<PERSON> b<PERSON>yük borsa"}, {"name": "Binance Futures", "features": ["futures"], "notes": "Sadece One-Way mode"}, {"name": "Bybit", "features": ["perpetual", "spot"], "notes": "USDT ve Inverse kontratlar"}, {"name": "OKX", "features": ["multi"], "notes": "<PERSON><PERSON><PERSON> ticaret t<PERSON>"}, {"name": "BitMEX", "features": ["derivatives"], "notes": "Bitcoin vadeli i<PERSON>"}, {"name": "Bitfinex", "features": ["spot", "margin"], "notes": "Spot ve margin"}, {"name": "Bitget", "features": ["multi"], "notes": "Çoklu özellikler"}, {"name": "<PERSON><PERSON><PERSON>", "features": ["futures", "options"], "notes": "Kripto opsiyonlar"}, {"name": "Binance US", "features": ["spot"], "notes": "ABD kullanıcıları"}], "api_setup_requirements": {"api_keys": "Her borsa için ayrı API anahtarları", "permissions": "Trading permissions gerekli", "ip_restrictions": "Gerekirse IP kısıtlamaları kaldırılmalı", "naming": "API anahtarlarına açıklayıcı isimler verilmeli"}, "webhook_format": {"basic_structure": "MyKeys(SYMBOL) { command(parameters); } #bot", "components": {"MyKeys": "API anahtar seti tanımlayıcısı", "SYMBOL": "Trading sembolü (ör: BTCUSDT)", "command": "Algoritmik trading komutu", "bot_tag": "Yönlendirme etiketi"}}}