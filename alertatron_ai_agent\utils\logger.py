"""
Advanced logging system for Alertatron AI Agent.
"""

import logging
import structlog
from typing import Optional, Dict, Any
from pathlib import Path
import sys
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

from ..config.settings import get_settings


class AlertatronLogger:
    """Advanced logging system with structured logging and rich formatting."""
    
    def __init__(self):
        self.settings = get_settings()
        self.console = Console()
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup structured logging with rich formatting."""
        
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # Setup standard logging
        logging.basicConfig(
            level=getattr(logging, self.settings.log_level.upper()),
            format="%(message)s",
            datefmt="[%X]",
            handlers=[
                RichHandler(
                    console=self.console,
                    rich_tracebacks=True,
                    tracebacks_show_locals=True
                )
            ]
        )
        
        # Add file handler if specified
        if self.settings.log_file:
            file_handler = logging.FileHandler(self.settings.log_file)
            file_handler.setFormatter(
                logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            )
            logging.getLogger().addHandler(file_handler)
    
    def get_logger(self, name: str) -> structlog.BoundLogger:
        """Get a structured logger instance."""
        return structlog.get_logger(name)
    
    def log_trade_execution(self, 
                          exchange: str, 
                          symbol: str, 
                          order_type: str, 
                          side: str, 
                          amount: float, 
                          price: Optional[float] = None,
                          order_id: Optional[str] = None,
                          status: str = "pending",
                          **kwargs):
        """Log trade execution with structured data."""
        logger = self.get_logger("trade_execution")
        
        trade_data = {
            "exchange": exchange,
            "symbol": symbol,
            "order_type": order_type,
            "side": side,
            "amount": amount,
            "price": price,
            "order_id": order_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        logger.info("Trade execution", **trade_data)
    
    def log_webhook_received(self, 
                           source: str, 
                           symbol: str, 
                           command: str, 
                           key_name: str,
                           raw_message: str,
                           **kwargs):
        """Log webhook reception with structured data."""
        logger = self.get_logger("webhook")
        
        webhook_data = {
            "source": source,
            "symbol": symbol,
            "command": command,
            "key_name": key_name,
            "raw_message": raw_message,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        logger.info("Webhook received", **webhook_data)
    
    def log_error(self, 
                  component: str, 
                  error_type: str, 
                  error_message: str, 
                  **kwargs):
        """Log errors with structured data."""
        logger = self.get_logger("error")
        
        error_data = {
            "component": component,
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        logger.error("System error", **error_data)
    
    def log_risk_management(self, 
                          action: str, 
                          reason: str, 
                          symbol: str,
                          exchange: str,
                          **kwargs):
        """Log risk management actions."""
        logger = self.get_logger("risk_management")
        
        risk_data = {
            "action": action,
            "reason": reason,
            "symbol": symbol,
            "exchange": exchange,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        logger.warning("Risk management action", **risk_data)


# Global logger instance
alertatron_logger = AlertatronLogger()


def get_logger(name: str = "alertatron") -> structlog.BoundLogger:
    """Get a logger instance."""
    return alertatron_logger.get_logger(name)


def log_trade_execution(**kwargs):
    """Convenience function for logging trade execution."""
    alertatron_logger.log_trade_execution(**kwargs)


def log_webhook_received(**kwargs):
    """Convenience function for logging webhook reception."""
    alertatron_logger.log_webhook_received(**kwargs)


def log_error(**kwargs):
    """Convenience function for logging errors."""
    alertatron_logger.log_error(**kwargs)


def log_risk_management(**kwargs):
    """Convenience function for logging risk management actions."""
    alertatron_logger.log_risk_management(**kwargs)
