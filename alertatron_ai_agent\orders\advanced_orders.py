"""
Advanced order implementations: ping<PERSON>ong, marketMaker, aggressiveEntry, dynamicTakeProfit, OCO, waitingLimit, managed.
"""

from typing import Dict, Any, List, Optional
import asyncio
import random
from datetime import datetime, timedelta

from .order_factory import OrderExecutor
from ..webhook.parser import ParsedCommand
from ..exchanges.base_connector import OrderSide
from ..utils.logger import log_trade_execution


class PingPongOrderExecutor(OrderExecutor):
    """Executor for ping pong trading orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a ping pong trading order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['range', 'profitMargin'],
                optional=['amount', 'maxCycles', 'startSide']
            )
            
            price_range = float(params['range'])  # Price range percentage
            profit_margin = float(params['profitMargin'])  # Profit margin percentage
            amount = float(params.get('amount', 100))
            max_cycles = int(params.get('maxCycles', 10))
            start_side = params.get('startSide', 'buy')
            
            current_price = await self.get_current_price(parsed_command.symbol)
            
            # Calculate ping pong range
            upper_price = current_price * (1 + price_range / 200)
            lower_price = current_price * (1 - price_range / 200)
            
            pingpong_id = f"pingpong_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            pingpong_order = {
                "id": pingpong_id,
                "symbol": parsed_command.symbol,
                "upper_price": upper_price,
                "lower_price": lower_price,
                "profit_margin": profit_margin,
                "amount": amount,
                "max_cycles": max_cycles,
                "current_cycle": 0,
                "current_side": start_side,
                "active_order": None,
                "total_profit": 0,
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Start ping pong execution
            asyncio.create_task(self._execute_ping_pong_cycle(pingpong_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='ping_pong',
                side=start_side,
                amount=amount,
                order_id=pingpong_id,
                status='started',
                price_range=f"{lower_price:.2f} - {upper_price:.2f}"
            )
            
            return {
                "status": "success",
                "order_type": "ping_pong",
                "order": pingpong_order,
                "price_range": f"{lower_price:.2f} - {upper_price:.2f}",
                "max_cycles": max_cycles,
                "message": "Ping pong trading started successfully"
            }
        
        except Exception as e:
            self.logger.error("Ping pong order execution failed", error=str(e))
            raise
    
    async def _execute_ping_pong_cycle(self, pingpong_order: Dict[str, Any]):
        """Execute ping pong trading cycles."""
        try:
            while (pingpong_order["current_cycle"] < pingpong_order["max_cycles"] and 
                   pingpong_order["status"] == "active"):
                
                # Determine order price and side
                if pingpong_order["current_side"] == "buy":
                    order_price = pingpong_order["lower_price"]
                    order_side = OrderSide.BUY
                    next_side = "sell"
                else:
                    order_price = pingpong_order["upper_price"]
                    order_side = OrderSide.SELL
                    next_side = "buy"
                
                # Place limit order
                order = await self.connector.create_limit_order(
                    symbol=pingpong_order["symbol"],
                    side=order_side,
                    amount=pingpong_order["amount"],
                    price=order_price
                )
                
                pingpong_order["active_order"] = order
                
                # Wait for order to fill
                await self._wait_for_order_fill(order, pingpong_order)
                
                # Update for next cycle
                pingpong_order["current_side"] = next_side
                pingpong_order["current_cycle"] += 1
                
                self.logger.info("Ping pong cycle completed",
                               pingpong_id=pingpong_order["id"],
                               cycle=pingpong_order["current_cycle"],
                               side=pingpong_order["current_side"])
            
            pingpong_order["status"] = "completed"
            self.logger.info("Ping pong trading completed", pingpong_id=pingpong_order["id"])
        
        except Exception as e:
            pingpong_order["status"] = "error"
            self.logger.error("Ping pong execution failed", pingpong_id=pingpong_order["id"], error=str(e))
    
    async def _wait_for_order_fill(self, order, pingpong_order):
        """Wait for order to be filled."""
        timeout = 3600  # 1 hour timeout
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < timeout:
            try:
                order_status = await self.connector.get_order_status(order.id, pingpong_order["symbol"])
                if order_status.status == 'closed':
                    break
            except:
                pass
            
            await asyncio.sleep(10)  # Check every 10 seconds


class MarketMakerOrderExecutor(OrderExecutor):
    """Executor for market making orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute a market making order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['spread'],
                optional=['amount', 'levels', 'skew', 'refreshRate']
            )
            
            spread = float(params['spread'])  # Spread percentage
            amount = float(params.get('amount', 100))
            levels = int(params.get('levels', 1))  # Number of levels on each side
            skew = float(params.get('skew', 0))  # Market skew (-1 to 1)
            refresh_rate = int(params.get('refreshRate', 30))  # Refresh rate in seconds
            
            current_price = await self.get_current_price(parsed_command.symbol)
            
            mm_id = f"marketmaker_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            market_maker_order = {
                "id": mm_id,
                "symbol": parsed_command.symbol,
                "spread": spread,
                "amount": amount,
                "levels": levels,
                "skew": skew,
                "refresh_rate": refresh_rate,
                "current_price": current_price,
                "buy_orders": [],
                "sell_orders": [],
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Start market making
            asyncio.create_task(self._execute_market_making(market_maker_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='market_maker',
                side='both',
                amount=amount * levels * 2,
                order_id=mm_id,
                status='started',
                spread=spread
            )
            
            return {
                "status": "success",
                "order_type": "market_maker",
                "order": market_maker_order,
                "spread": spread,
                "levels": levels,
                "message": "Market making started successfully"
            }
        
        except Exception as e:
            self.logger.error("Market maker order execution failed", error=str(e))
            raise
    
    async def _execute_market_making(self, mm_order: Dict[str, Any]):
        """Execute market making strategy."""
        try:
            while mm_order["status"] == "active":
                # Cancel existing orders
                await self._cancel_existing_orders(mm_order)
                
                # Get current price
                current_price = await self.get_current_price(mm_order["symbol"])
                mm_order["current_price"] = current_price
                
                # Calculate bid/ask prices with skew
                base_spread = mm_order["spread"] / 100
                skewed_spread_buy = base_spread * (1 + mm_order["skew"])
                skewed_spread_sell = base_spread * (1 - mm_order["skew"])
                
                # Place buy and sell orders at multiple levels
                for level in range(mm_order["levels"]):
                    level_multiplier = 1 + (level * 0.1)  # Increase spread for each level
                    
                    # Buy orders
                    buy_price = current_price * (1 - skewed_spread_buy * level_multiplier)
                    buy_order = await self.connector.create_limit_order(
                        symbol=mm_order["symbol"],
                        side=OrderSide.BUY,
                        amount=mm_order["amount"],
                        price=buy_price
                    )
                    mm_order["buy_orders"].append(buy_order)
                    
                    # Sell orders
                    sell_price = current_price * (1 + skewed_spread_sell * level_multiplier)
                    sell_order = await self.connector.create_limit_order(
                        symbol=mm_order["symbol"],
                        side=OrderSide.SELL,
                        amount=mm_order["amount"],
                        price=sell_price
                    )
                    mm_order["sell_orders"].append(sell_order)
                
                # Wait for refresh interval
                await asyncio.sleep(mm_order["refresh_rate"])
        
        except Exception as e:
            mm_order["status"] = "error"
            self.logger.error("Market making execution failed", mm_id=mm_order["id"], error=str(e))
    
    async def _cancel_existing_orders(self, mm_order: Dict[str, Any]):
        """Cancel existing market making orders."""
        all_orders = mm_order["buy_orders"] + mm_order["sell_orders"]
        
        for order in all_orders:
            try:
                await self.connector.cancel_order(order.id, mm_order["symbol"])
            except:
                pass  # Order might already be filled or canceled
        
        mm_order["buy_orders"] = []
        mm_order["sell_orders"] = []


class AggressiveEntryOrderExecutor(OrderExecutor):
    """Executor for aggressive entry orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute an aggressive entry order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['side', 'amount'],
                optional=['slippage', 'maxPrice', 'timeLimit', 'chaseLimit']
            )
            
            side = params['side']
            amount = float(params['amount'])
            slippage = float(params.get('slippage', 1))  # 1% default slippage
            time_limit = int(params.get('timeLimit', 300))  # 5 minutes default
            chase_limit = int(params.get('chaseLimit', 5))  # Max 5 chase attempts
            
            current_price = await self.get_current_price(parsed_command.symbol)
            
            # Calculate max acceptable price with slippage
            if side.lower() == 'buy':
                max_price = current_price * (1 + slippage / 100)
            else:
                max_price = current_price * (1 - slippage / 100)
            
            if 'maxPrice' in params:
                max_price = min(max_price, float(params['maxPrice']))
            
            aggressive_id = f"aggressive_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            aggressive_order = {
                "id": aggressive_id,
                "symbol": parsed_command.symbol,
                "side": side,
                "amount": amount,
                "max_price": max_price,
                "time_limit": time_limit,
                "chase_limit": chase_limit,
                "chase_attempts": 0,
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Start aggressive entry execution
            result = await self._execute_aggressive_entry(aggressive_order)
            
            return result
        
        except Exception as e:
            self.logger.error("Aggressive entry order execution failed", error=str(e))
            raise
    
    async def _execute_aggressive_entry(self, aggressive_order: Dict[str, Any]) -> Dict[str, Any]:
        """Execute aggressive entry with price chasing."""
        try:
            order_side = OrderSide.BUY if aggressive_order["side"].lower() == 'buy' else OrderSide.SELL
            start_time = datetime.now()
            
            while ((datetime.now() - start_time).seconds < aggressive_order["time_limit"] and
                   aggressive_order["chase_attempts"] < aggressive_order["chase_limit"]):
                
                current_price = await self.get_current_price(aggressive_order["symbol"])
                
                # Check if price is still within acceptable range
                if aggressive_order["side"].lower() == 'buy':
                    if current_price > aggressive_order["max_price"]:
                        break  # Price too high
                    execution_price = min(current_price * 1.001, aggressive_order["max_price"])  # Slightly above market
                else:
                    if current_price < aggressive_order["max_price"]:
                        break  # Price too low
                    execution_price = max(current_price * 0.999, aggressive_order["max_price"])  # Slightly below market
                
                # Try to execute order
                try:
                    order = await self.connector.create_limit_order(
                        symbol=aggressive_order["symbol"],
                        side=order_side,
                        amount=aggressive_order["amount"],
                        price=execution_price
                    )
                    
                    # Wait briefly to see if order fills
                    await asyncio.sleep(2)
                    
                    order_status = await self.connector.get_order_status(order.id, aggressive_order["symbol"])
                    
                    if order_status.status == 'closed':
                        # Order filled successfully
                        log_trade_execution(
                            exchange=self.connector.exchange_name,
                            symbol=aggressive_order["symbol"],
                            order_type='aggressive_entry',
                            side=aggressive_order["side"],
                            amount=aggressive_order["amount"],
                            price=execution_price,
                            order_id=order.id,
                            status='filled'
                        )
                        
                        return {
                            "status": "success",
                            "order_type": "aggressive_entry",
                            "order": order.dict(),
                            "execution_price": execution_price,
                            "chase_attempts": aggressive_order["chase_attempts"],
                            "message": "Aggressive entry executed successfully"
                        }
                    
                    else:
                        # Order not filled, cancel and try again
                        await self.connector.cancel_order(order.id, aggressive_order["symbol"])
                        aggressive_order["chase_attempts"] += 1
                
                except Exception as e:
                    self.logger.warning("Aggressive entry attempt failed", attempt=aggressive_order["chase_attempts"], error=str(e))
                    aggressive_order["chase_attempts"] += 1
                
                await asyncio.sleep(1)  # Wait before next attempt
            
            # If we get here, aggressive entry failed
            return {
                "status": "failed",
                "order_type": "aggressive_entry",
                "message": "Aggressive entry failed - time limit or chase limit exceeded",
                "chase_attempts": aggressive_order["chase_attempts"]
            }
        
        except Exception as e:
            self.logger.error("Aggressive entry execution failed", error=str(e))
            raise


class OCOOrderExecutor(OrderExecutor):
    """Executor for One-Cancels-Other orders."""
    
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute an OCO order."""
        try:
            params = self.validate_parameters(
                parsed_command.parameters,
                required=['order1', 'order2'],
                optional=['amount']
            )
            
            # Parse the two orders
            order1_params = params['order1']
            order2_params = params['order2']
            amount = float(params.get('amount', 100))
            
            oco_id = f"oco_{parsed_command.symbol}_{datetime.now().timestamp()}"
            
            # Create both orders
            order1 = await self._create_oco_leg(parsed_command.symbol, order1_params, amount)
            order2 = await self._create_oco_leg(parsed_command.symbol, order2_params, amount)
            
            oco_order = {
                "id": oco_id,
                "symbol": parsed_command.symbol,
                "order1": order1,
                "order2": order2,
                "status": "active",
                "created_at": datetime.now()
            }
            
            # Start OCO monitoring
            asyncio.create_task(self._monitor_oco_orders(oco_order))
            
            log_trade_execution(
                exchange=self.connector.exchange_name,
                symbol=parsed_command.symbol,
                order_type='oco',
                side='both',
                amount=amount,
                order_id=oco_id,
                status='placed'
            )
            
            return {
                "status": "success",
                "order_type": "oco",
                "order": oco_order,
                "message": "OCO order placed successfully"
            }
        
        except Exception as e:
            self.logger.error("OCO order execution failed", error=str(e))
            raise
    
    async def _create_oco_leg(self, symbol: str, order_params: Dict[str, Any], amount: float):
        """Create one leg of the OCO order."""
        order_type = order_params.get('type', 'limit')
        side = order_params['side']
        price = float(order_params['price'])
        
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
        
        if order_type == 'limit':
            return await self.connector.create_limit_order(symbol, order_side, amount, price)
        elif order_type == 'stop':
            return await self.connector.create_stop_order(symbol, order_side, amount, price)
        else:
            raise ValueError(f"Unsupported OCO order type: {order_type}")
    
    async def _monitor_oco_orders(self, oco_order: Dict[str, Any]):
        """Monitor OCO orders and cancel the other when one fills."""
        try:
            while oco_order["status"] == "active":
                # Check status of both orders
                order1_status = await self.connector.get_order_status(oco_order["order1"].id, oco_order["symbol"])
                order2_status = await self.connector.get_order_status(oco_order["order2"].id, oco_order["symbol"])
                
                if order1_status.status == 'closed':
                    # Order 1 filled, cancel order 2
                    await self.connector.cancel_order(oco_order["order2"].id, oco_order["symbol"])
                    oco_order["status"] = "order1_filled"
                    break
                
                elif order2_status.status == 'closed':
                    # Order 2 filled, cancel order 1
                    await self.connector.cancel_order(oco_order["order1"].id, oco_order["symbol"])
                    oco_order["status"] = "order2_filled"
                    break
                
                await asyncio.sleep(5)  # Check every 5 seconds
        
        except Exception as e:
            self.logger.error("OCO monitoring failed", oco_id=oco_order["id"], error=str(e))
