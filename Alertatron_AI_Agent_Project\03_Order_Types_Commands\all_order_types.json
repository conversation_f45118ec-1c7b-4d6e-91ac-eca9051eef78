{"market_order": {"name": "Market Order (Piyasa Emri)", "syntax": "market(side, amount, position, reduceOnly)", "description": "Güncel fiyattan anında gerçekleştirilen piyasa emri", "parameters": {"side": {"type": "string", "required": true, "values": ["buy", "sell"]}, "amount": {"type": "mixed", "formats": ["1000", "50%", "50%x", "50%p"]}, "position": {"type": "mixed", "description": "<PERSON><PERSON><PERSON> p<PERSON> büyüklüğü"}, "reduceOnly": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> poz<PERSON>"}}, "examples": ["market(side=buy, amount=1000)", "market(position=0)"]}, "limit_order": {"name": "Limit Order (Limit Emri)", "syntax": "limit(side, amount, offset, postOnly, reduceOnly, position, tag)", "description": "Belirli fiyattan limit <PERSON><PERSON>", "parameters": {"side": {"type": "string", "required": true}, "amount": {"type": "number", "required": true}, "offset": {"type": "string", "formats": ["1%", "50", "e1%", "@50000"]}, "postOnly": {"type": "boolean", "default": true}}}, "stop_order": {"name": "Stop Order", "syntax": "stop(...)", "description": "Stop emri"}, "trailing_stop_order": {"name": "Trailing Stop", "syntax": "trailingStop(...)", "description": "Takip eden stop"}, "trailing_take_profit": {"name": "Trailing Take Profit", "syntax": "trailingTakeProfit(...)", "description": "Ta<PERSON>p eden kar al"}, "iceberg_order": {"name": "Iceberg Order", "syntax": "iceberg(...)", "description": "Buzdağı emri"}, "grid_order": {"name": "Grid Order", "syntax": "grid(...)", "description": "<PERSON><PERSON><PERSON> em<PERSON>"}, "twap_order": {"name": "TWAP Order", "syntax": "twap(...)", "description": "Zaman ağırlıklı ortalama"}, "ping_pong_order": {"name": "Ping Pong Order", "syntax": "ping<PERSON><PERSON>(...)", "description": "<PERSON><PERSON><PERSON><PERSON> alım-satım"}, "market_maker_order": {"name": "Market Maker", "syntax": "marketMaker(...)", "description": "Market maker emri"}, "aggressive_entry_order": {"name": "Aggressive Entry", "syntax": "aggressiveEntry(...)", "description": "<PERSON>g<PERSON><PERSON>"}, "dynamic_take_profit": {"name": "Dynamic Take Profit", "syntax": "dynamicTakeProfit(...)", "description": "<PERSON><PERSON><PERSON> kar al"}, "one_cancels_other": {"name": "One Cancels Other", "syntax": "oco(...)", "description": "OCO emri"}, "scaled_order": {"name": "Scaled Order", "syntax": "scaled(...)", "description": "<PERSON><PERSON><PERSON><PERSON> emir"}, "waiting_limit_order": {"name": "Waiting Limit", "syntax": "waitingLimit(...)", "description": "Bekleyen limit emir"}}