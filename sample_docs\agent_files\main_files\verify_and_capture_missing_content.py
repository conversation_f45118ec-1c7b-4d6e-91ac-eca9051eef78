
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: verify_and_capture_missing_content.py
# execution: true
from api_server.agent_tools.ask_question_about_documents import ask_question_about_documents

print("Verifying and capturing missing textual content...")
print("="*80)

# Re-extract the core technical information with explicit content capture
critical_urls = [
    "https://alertatron.com/docs/automated-trading/basic-concepts",
    "https://alertatron.com/docs/automated-trading/command-reference"
]

# Extract and explicitly capture the technical specifications
technical_query = """
Extract and provide ALL technical specifications in complete detail:

1. EXACT command syntax for market orders including all parameters
2. COMPLETE list of all supported exchanges with their exact names
3. FULL list of all order types with their exact names and functions
4. PRECISE API integration requirements and procedures
5. EXACT TradingView webhook integration format and syntax
6. COMPLETE risk management features and commands
7. ALL security requirements and best practices

Please provide the complete technical specifications that would be needed to implement an algorithmic trading system. Include exact syntax, parameter names, and procedural steps.
"""

print("Extracting comprehensive technical specifications...")
csv_answer, json_answer, rag_answer, advanced_rag_answers = ask_question_about_documents(
    query=technical_query,
    urls=critical_urls
)

# Save this technical content to a dedicated file
with open("alertatron_technical_specifications.txt", "w", encoding='utf-8') as f:
    f.write("ALERTATRON TECHNICAL SPECIFICATIONS\n")
    f.write("="*50 + "\n\n")
    f.write("EXTRACTION DATE: 2025-07-11\n")
    f.write("SOURCE: https://alertatron.com/docs/automated-trading/\n\n")
    f.write("COMPLETE TECHNICAL CONTENT:\n")
    f.write("-" * 30 + "\n")
    f.write(rag_answer if rag_answer else "No RAG answer available")
    f.write("\n\n")
    if advanced_rag_answers:
        f.write("ADVANCED RAG ANSWERS:\n")
        f.write("-" * 30 + "\n")
        f.write(str(advanced_rag_answers))

print("="*80)
print("Technical specifications captured and verified.")
print("Saved to: alertatron_technical_specifications.txt")
print("="*80)