{"links": [["No visible text", "https://alertatron.com/register"], ["Blog", "https://alertatron.com/blog"], ["No visible text", "https://alertatron.com/docs/automated-trading/basic-concepts#"], ["<PERSON><PERSON>", "https://alertatron.com/login"], ["Getting Started", "https://alertatron.com/docs/all/guide"], ["Signal Lite", "https://alertatron.com/docs/getting-started-with-signals-lite"], ["Algo Trading", "https://alertatron.com/docs/automated-trading/common-examples"], ["Change / Renew Plan", "https://alertatron.com/settings/billing"], ["Receipts", "https://alertatron.com/settings/billing-history"], ["T&C's", "https://alertatron.com/terms"], ["Privacy Policy", "https://alertatron.com/privacypolicy"], ["Support", "https://alertatron.com/support"], ["Basic Concepts", "https://alertatron.com/docs/automated-trading/basic-concepts"], ["API Keys : Bybit", "https://alertatron.com/docs/automated-trading/api-keys-bybit"], ["API Keys : Binance Spot", "https://alertatron.com/docs/automated-trading/api-keys-binance-spot"], ["API Keys : Binance Futures", "https://alertatron.com/docs/automated-trading/api-keys-binance-futures"], ["API Keys : Binance US", "https://alertatron.com/docs/automated-trading/api-keys-binance-us"], ["API Keys : OKX", "https://alertatron.com/docs/automated-trading/api-keys-okx"], ["API Keys : Bitfinex Spot and Margin", "https://alertatron.com/docs/automated-trading/api-keys-bitfinex-spot-and-margin"], ["API Keys : BitMEX", "https://alertatron.com/docs/automated-trading/api-keys-bitmex"], ["API Keys : Bitget", "https://alertatron.com/docs/automated-trading/api-keys-bitget"], ["API Keys : Deribit Futures", "https://alertatron.com/docs/automated-trading/api-keys-deribit-futures"], ["Trading History API", "https://alertatron.com/docs/automated-trading/trading-history-api"], ["Version History", "https://alertatron.com/docs/automated-trading/version-history"], ["Order, Stop Loss and Take Profit", "https://alertatron.com/docs/automated-trading/order-stop-loss-and-take-profit"], ["Closing any open position", "https://alertatron.com/docs/automated-trading/closing-any-open-position"], ["Pyramiding and Multiple Entries", "https://alertatron.com/docs/automated-trading/pyramiding-and-multiple-entries"], ["Understanding Amount & Position", "https://alertatron.com/docs/automated-trading/understanding-amount-position"], ["Aggressive Entry Order", "https://alertatron.com/docs/automated-trading/aggressive-entry-order"], ["Balance", "https://alertatron.com/docs/automated-trading/balance"], ["Cancel Orders", "https://alertatron.com/docs/automated-trading/cancel-orders"], ["Continue", "https://alertatron.com/docs/automated-trading/continue"], ["Dynamic Take Profit", "https://alertatron.com/docs/automated-trading/dynamic-take-profit"], ["Exchange Settings", "https://alertatron.com/docs/automated-trading/exchange-settings"], ["Grid Order", "https://alertatron.com/docs/automated-trading/grid-order"], ["Iceberg Order", "https://alertatron.com/docs/automated-trading/iceberg-order"], ["Limit Order", "https://alertatron.com/docs/automated-trading/limit-order"], ["Managed Order", "https://alertatron.com/docs/automated-trading/managed-order"], ["Market Maker Order", "https://alertatron.com/docs/automated-trading/market-maker-order"], ["Market Order", "https://alertatron.com/docs/automated-trading/market-order"], ["One Cancels Other", "https://alertatron.com/docs/automated-trading/one-cancels-other"], ["Ping Pong Order", "https://alertatron.com/docs/automated-trading/ping-pong-order"], ["Scaled Order", "https://alertatron.com/docs/automated-trading/scaled-order"], ["Stop", "https://alertatron.com/docs/automated-trading/stop"], ["Stop or Take Profit Order", "https://alertatron.com/docs/automated-trading/stop-or-take-profit-order"], ["Stop Order", "https://alertatron.com/docs/automated-trading/stop-order"], ["Trailing Limit Order", "https://alertatron.com/docs/automated-trading/trailing-limit-order"], ["Trailing Stop Order", "https://alertatron.com/docs/automated-trading/trailing-stop-order"], ["Trailing Take Profit Order", "https://alertatron.com/docs/automated-trading/trailing-take-profit-order"], ["TWAP Time-Weighted Average Price Order", "https://alertatron.com/docs/automated-trading/twap-time-weighted-average-price-order"], ["Wait", "https://alertatron.com/docs/automated-trading/wait"], ["Waiting Limit Order", "https://alertatron.com/docs/automated-trading/waiting-limit-order"], ["Running a Trading Group on Alertatron", "https://alertatron.com/docs/automated-trading/running-a-trading-group-on-alertatron"], ["Signals in Trading Groups", "https://alertatron.com/docs/automated-trading/signals-in-trading-groups"], ["Trading Group API", "https://alertatron.com/docs/automated-trading/trading-group-api"], ["Learn how to do this...", "https://alertatron.com/docs/getting-alerts-from-tradingview"], ["different commands", "https://alertatron.com/docs/automated-trading/command-reference"], ["examples available", "https://alertatron.com/docs/automated-trading/automated-trading-examples"], ["<PERSON><PERSON><PERSON>", "https://testapp.deribit.com/main#/futures?tab=BTC-PERPETUAL"], ["BitMEX", "https://testnet.bitmex.com/app/trade/XBTUSD"], ["Unsplash", "https://unsplash.com/"], ["Learn more", "https://alertatron.com/"]], "images": [{"alt": "Alertatron.com Logo", "class": "logo-light", "src": "/img/email/alertatron-wide-dark.png", "title": "beep bop - <PERSON>, I'm <PERSON>, the Alertatron bot.", "url": "/img/email/alertatron-wide-dark.png", "is_animated": false, "frame_count": 0, "animation_detection_method": "none"}, {"alt": "Alertatron.com Logo", "class": "logo-dark", "src": "/img/email/alertatron-wide.png", "title": "beep bop - <PERSON>, I'm <PERSON>, the Alertatron bot.", "url": "/img/email/alertatron-wide.png", "is_animated": false, "frame_count": 0, "animation_detection_method": "none"}]}