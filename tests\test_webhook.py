"""
Tests for webhook handling functionality.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from datetime import datetime

from alertatron_ai_agent.webhook.handler import <PERSON>hook<PERSON>and<PERSON>
from alertatron_ai_agent.webhook.validator import WebhookValidator
from alertatron_ai_agent.webhook.parser import CommandParser


class TestWebhookValidator:
    """Test webhook validation functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.validator = WebhookValidator()
    
    def test_valid_webhook_message(self):
        """Test validation of valid webhook message."""
        webhook_data = {
            "message": "MyKeys(BTCUSDT) { market(side=buy, amount=100); } #bot"
        }
        
        is_valid, error = self.validator.validate_webhook(webhook_data)
        # Note: This will fail without proper API keys setup, but tests the parsing
        assert isinstance(is_valid, bool)
        assert error is None or isinstance(error, str)
    
    def test_invalid_webhook_format(self):
        """Test validation of invalid webhook format."""
        webhook_data = {
            "message": "invalid message format"
        }
        
        is_valid, error = self.validator.validate_webhook(webhook_data)
        assert is_valid is False
        assert "format" in error.lower()
    
    def test_missing_message(self):
        """Test validation with missing message."""
        webhook_data = {}
        
        is_valid, error = self.validator.validate_webhook(webhook_data)
        assert is_valid is False
        assert "message" in error.lower()
    
    def test_get_validation_info(self):
        """Test detailed validation info."""
        message = "TestKeys(ETHUSDT) { limit(side=sell, amount=50, offset=1%); } #bot"
        
        info = self.validator.get_validation_info(message)
        assert isinstance(info, dict)
        assert "is_valid" in info
        assert "components" in info
        
        if info["components"]:
            assert "key_name" in info["components"]
            assert "symbol" in info["components"]
            assert "command" in info["components"]


class TestCommandParser:
    """Test command parsing functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.parser = CommandParser()
    
    def test_parse_market_order(self):
        """Test parsing market order command."""
        message = "MyKeys(BTCUSDT) { market(side=buy, amount=100); } #bot"
        
        parsed = self.parser.parse_webhook_message(message)
        assert parsed is not None
        assert parsed.key_name == "MyKeys"
        assert parsed.symbol == "BTCUSDT"
        assert parsed.command == "market"
        assert parsed.parameters["side"] == "buy"
        assert parsed.parameters["amount"] == 100
    
    def test_parse_limit_order(self):
        """Test parsing limit order command."""
        message = "TestKeys(ETHUSDT) { limit(side=sell, amount=50, offset='1%'); } #bot"
        
        parsed = self.parser.parse_webhook_message(message)
        assert parsed is not None
        assert parsed.command == "limit"
        assert parsed.parameters["side"] == "sell"
        assert parsed.parameters["amount"] == 50
        assert parsed.parameters["offset"] == "1%"
    
    def test_parse_complex_parameters(self):
        """Test parsing complex parameter combinations."""
        message = "Keys(ADAUSDT) { stop(side=buy, amount=1000, offset=e2%, postOnly=true); } #bot"
        
        parsed = self.parser.parse_webhook_message(message)
        assert parsed is not None
        assert parsed.parameters["postOnly"] is True
        assert parsed.parameters["offset"] == "e2%"
    
    def test_invalid_message_format(self):
        """Test parsing invalid message format."""
        message = "invalid format"
        
        parsed = self.parser.parse_webhook_message(message)
        assert parsed is None
    
    def test_get_command_info(self):
        """Test getting command information."""
        info = self.parser.get_command_info("market")
        assert isinstance(info, dict)
        assert "description" in info
        
        info = self.parser.get_command_info("unknown_command")
        assert isinstance(info, dict)


class TestWebhookHandler:
    """Test webhook handler functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.handler = WebhookHandler()
    
    def test_parse_webhook_message(self):
        """Test webhook message parsing."""
        webhook_data = {
            "message": "TestKeys(BTCUSDT) { market(side=buy, amount=100); } #bot"
        }
        
        parsed = self.handler.parse_webhook_message(webhook_data)
        assert parsed is not None
        assert parsed.symbol == "BTCUSDT"
        assert parsed.command == "market"
    
    @pytest.mark.asyncio
    async def test_process_webhook_invalid_message(self):
        """Test processing webhook with invalid message."""
        webhook_data = {
            "message": "invalid format"
        }
        
        result = await self.handler.process_webhook(webhook_data)
        assert result["status"] == "error"
        assert "parse" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_test_webhook_processing(self):
        """Test webhook processing test function."""
        test_message = "TestKeys(BTCUSDT) { market(side=buy, amount=100); } #bot"
        
        result = await self.handler.test_webhook_processing(test_message)
        assert isinstance(result, dict)
        assert "status" in result
    
    def test_get_processing_stats(self):
        """Test getting processing statistics."""
        stats = self.handler.get_processing_stats()
        assert isinstance(stats, dict)
        assert "total_webhooks_processed" in stats
        assert "status" in stats


@pytest.fixture
def sample_webhook_data():
    """Sample webhook data for testing."""
    return {
        "message": "MyKeys(BTCUSDT) { market(side=buy, amount=100); } #bot",
        "timestamp": datetime.now().isoformat(),
        "source": "tradingview"
    }


@pytest.fixture
def sample_parsed_command():
    """Sample parsed command for testing."""
    from alertatron_ai_agent.webhook.parser import ParsedCommand
    
    return ParsedCommand(
        key_name="TestKeys",
        symbol="BTCUSDT",
        command="market",
        parameters={"side": "buy", "amount": 100},
        bot_tag="bot",
        raw_message="TestKeys(BTCUSDT) { market(side=buy, amount=100); } #bot"
    )


class TestWebhookIntegration:
    """Integration tests for webhook functionality."""
    
    @pytest.mark.asyncio
    async def test_full_webhook_flow(self, sample_webhook_data):
        """Test complete webhook processing flow."""
        handler = WebhookHandler()
        
        # This will fail without proper setup, but tests the flow
        result = await handler.process_webhook(sample_webhook_data)
        assert isinstance(result, dict)
        assert "status" in result
    
    def test_webhook_validation_flow(self, sample_webhook_data):
        """Test webhook validation flow."""
        validator = WebhookValidator()
        
        is_valid, error = validator.validate_webhook(sample_webhook_data)
        assert isinstance(is_valid, bool)
        
        if not is_valid:
            assert isinstance(error, str)
            assert len(error) > 0
