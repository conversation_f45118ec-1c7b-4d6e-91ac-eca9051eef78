"""
Exchange manager for handling multiple cryptocurrency exchanges.
"""

from typing import Dict, Any, Optional, List, Type
import asyncio

from .base_connector import BaseExchangeConnector
from .binance_connector import BinanceConnector, BinanceFuturesConnector
from .bybit_connector import BybitConnector
from ..config.api_keys import get_api_key_manager, ExchangeCredentials
from ..config.settings import get_settings, get_exchange_config
from ..utils.logger import get_logger


logger = get_logger("exchange_manager")


class ExchangeManager:
    """Manages connections to multiple cryptocurrency exchanges."""
    
    def __init__(self):
        self.api_key_manager = get_api_key_manager()
        self.settings = get_settings()
        
        # Registry of exchange connector classes
        self.exchange_classes: Dict[str, Type[BaseExchangeConnector]] = {
            'binance': BinanceConnector,
            'binanceusdm': BinanceFuturesConnector,
            'binanceus': BinanceConnector,  # Same as regular Binance for now
            'bybit': BybitConnector,
            # Will add other exchanges here as they are implemented
            # 'okx': OKXConnector,
            # 'bitmex': BitMEXConnector,
            # 'bitfinex': BitfinexConnector,
            # 'bitget': BitgetConnector,
            # 'deribit': DeribitConnector,
        }
        
        # Active exchange connections
        self.connections: Dict[str, Dict[str, BaseExchangeConnector]] = {}
        
        # Initialize connections
        asyncio.create_task(self._initialize_connections())
    
    async def _initialize_connections(self):
        """Initialize connections to all configured exchanges."""
        try:
            available_keys = self.api_key_manager.list_available_keys()
            
            for exchange_name, key_names in available_keys.items():
                if exchange_name in self.exchange_classes:
                    self.connections[exchange_name] = {}
                    
                    for key_name in key_names:
                        try:
                            await self._create_connection(exchange_name, key_name)
                        except Exception as e:
                            logger.error("Failed to initialize connection",
                                       exchange=exchange_name,
                                       key_name=key_name,
                                       error=str(e))
            
            logger.info("Exchange manager initialized",
                       total_exchanges=len(self.connections),
                       total_connections=sum(len(conns) for conns in self.connections.values()))
        
        except Exception as e:
            logger.error("Failed to initialize exchange manager", error=str(e))
    
    async def _create_connection(self, exchange_name: str, key_name: str) -> BaseExchangeConnector:
        """Create a connection to an exchange with specific API keys."""
        try:
            # Get credentials
            credentials = self.api_key_manager.get_credentials(exchange_name, key_name)
            if not credentials:
                raise ValueError(f"No credentials found for {exchange_name}:{key_name}")
            
            # Get exchange configuration
            exchange_config = get_exchange_config(exchange_name)
            
            # Get connector class
            connector_class = self.exchange_classes.get(exchange_name)
            if not connector_class:
                raise ValueError(f"No connector available for exchange: {exchange_name}")
            
            # Create connector instance
            connector = connector_class(credentials, exchange_config)
            
            # Test connection
            connected = await connector.connect()
            if not connected:
                raise Exception("Failed to connect to exchange")
            
            # Store connection
            if exchange_name not in self.connections:
                self.connections[exchange_name] = {}
            
            self.connections[exchange_name][key_name] = connector
            
            logger.info("Exchange connection created",
                       exchange=exchange_name,
                       key_name=key_name)
            
            return connector
        
        except Exception as e:
            logger.error("Failed to create exchange connection",
                        exchange=exchange_name,
                        key_name=key_name,
                        error=str(e))
            raise
    
    def get_connector(self, exchange_name: str, key_name: str) -> Optional[BaseExchangeConnector]:
        """Get a specific exchange connector."""
        return self.connections.get(exchange_name, {}).get(key_name)
    
    def find_connector_for_key(self, key_name: str) -> Optional[BaseExchangeConnector]:
        """Find the first connector that uses the specified key name."""
        for exchange_name, connectors in self.connections.items():
            if key_name in connectors:
                return connectors[key_name]
        return None
    
    async def get_all_balances(self) -> Dict[str, Dict[str, Any]]:
        """Get balances from all connected exchanges."""
        all_balances = {}
        
        for exchange_name, connectors in self.connections.items():
            all_balances[exchange_name] = {}
            
            for key_name, connector in connectors.items():
                try:
                    balance = await connector.get_balance()
                    all_balances[exchange_name][key_name] = {
                        currency: balance_info.dict() 
                        for currency, balance_info in balance.items()
                        if balance_info.total > 0
                    }
                except Exception as e:
                    logger.error("Failed to get balance",
                               exchange=exchange_name,
                               key_name=key_name,
                               error=str(e))
                    all_balances[exchange_name][key_name] = {"error": str(e)}
        
        return all_balances
    
    async def get_all_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get positions from all connected exchanges."""
        all_positions = {}
        
        for exchange_name, connectors in self.connections.items():
            all_positions[exchange_name] = {}
            
            for key_name, connector in connectors.items():
                try:
                    positions = await connector.get_positions()
                    all_positions[exchange_name][key_name] = [
                        pos.dict() for pos in positions
                    ]
                except Exception as e:
                    logger.error("Failed to get positions",
                               exchange=exchange_name,
                               key_name=key_name,
                               error=str(e))
                    all_positions[exchange_name][key_name] = {"error": str(e)}
        
        return all_positions
    
    async def test_all_connections(self) -> Dict[str, Dict[str, Any]]:
        """Test all exchange connections."""
        test_results = {}
        
        for exchange_name, connectors in self.connections.items():
            test_results[exchange_name] = {}
            
            for key_name, connector in connectors.items():
                try:
                    result = await connector.test_connection()
                    test_results[exchange_name][key_name] = result
                except Exception as e:
                    test_results[exchange_name][key_name] = {
                        "connected": False,
                        "error": str(e),
                        "status": "Test failed"
                    }
        
        return test_results
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get status of all connections."""
        status = {
            "total_exchanges": len(self.connections),
            "total_connections": sum(len(conns) for conns in self.connections.values()),
            "exchanges": {}
        }
        
        for exchange_name, connectors in self.connections.items():
            status["exchanges"][exchange_name] = {
                "total_connections": len(connectors),
                "connections": {
                    key_name: {
                        "connected": connector.is_connected,
                        "exchange_name": connector.exchange_name
                    }
                    for key_name, connector in connectors.items()
                }
            }
        
        return status
    
    async def disconnect_all(self):
        """Disconnect from all exchanges."""
        for exchange_name, connectors in self.connections.items():
            for key_name, connector in connectors.items():
                try:
                    await connector.disconnect()
                except Exception as e:
                    logger.error("Error disconnecting",
                               exchange=exchange_name,
                               key_name=key_name,
                               error=str(e))
        
        self.connections.clear()
        logger.info("All exchange connections closed")


# Global exchange manager instance
exchange_manager = ExchangeManager()


def get_exchange_manager() -> ExchangeManager:
    """Get the global exchange manager instance."""
    return exchange_manager
