"""
Order factory for creating different types of orders.
"""

from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

from ..webhook.parser import ParsedCommand
from ..exchanges.base_connector import BaseExchangeConnector
from ..utils.logger import get_logger


logger = get_logger("order_factory")


class OrderExecutor(ABC):
    """Base class for order executors."""
    
    def __init__(self, connector: BaseExchangeConnector):
        self.connector = connector
        self.logger = get_logger(f"order_executor_{self.__class__.__name__.lower()}")
    
    @abstractmethod
    async def execute(self, parsed_command: ParsedCommand) -> Dict[str, Any]:
        """Execute the order based on parsed command."""
        pass
    
    def validate_parameters(self, params: Dict[str, Any], required: list, optional: list = None) -> Dict[str, Any]:
        """Validate and normalize order parameters."""
        optional = optional or []
        validated = {}
        
        # Check required parameters
        for param in required:
            if param not in params:
                raise ValueError(f"Required parameter '{param}' is missing")
            validated[param] = params[param]
        
        # Add optional parameters if present
        for param in optional:
            if param in params:
                validated[param] = params[param]
        
        return validated
    
    async def get_current_price(self, symbol: str) -> float:
        """Get current market price for a symbol."""
        try:
            ticker = await self.connector.get_ticker(symbol)
            return float(ticker.get('last', 0))
        except Exception as e:
            self.logger.error("Failed to get current price", symbol=symbol, error=str(e))
            raise ValueError(f"Could not get current price for {symbol}")
    
    def calculate_price_from_offset(self, current_price: float, offset: str, side: str) -> float:
        """Calculate actual price from offset specification."""
        try:
            # Handle different offset formats
            if offset.startswith('@'):
                # Absolute price: @50000
                return float(offset[1:])
            
            elif offset.startswith('e'):
                # Entry offset: e50 or e1%
                offset_value = offset[1:]
                if '%' in offset_value:
                    percentage = float(offset_value.replace('%', ''))
                    if side.lower() == 'buy':
                        return current_price * (1 - percentage / 100)
                    else:
                        return current_price * (1 + percentage / 100)
                else:
                    # Fixed offset from current price
                    fixed_offset = float(offset_value)
                    if side.lower() == 'buy':
                        return current_price - fixed_offset
                    else:
                        return current_price + fixed_offset
            
            elif '%' in offset:
                # Percentage offset: 1%
                percentage = float(offset.replace('%', ''))
                if side.lower() == 'buy':
                    return current_price * (1 - percentage / 100)
                else:
                    return current_price * (1 + percentage / 100)
            
            else:
                # Fixed offset: 50
                fixed_offset = float(offset)
                if side.lower() == 'buy':
                    return current_price - fixed_offset
                else:
                    return current_price + fixed_offset
        
        except Exception as e:
            raise ValueError(f"Invalid offset format: {offset}")
    
    def calculate_amount_from_specification(self, amount_spec: str, symbol: str, current_price: float) -> float:
        """Calculate actual amount from amount specification."""
        try:
            if isinstance(amount_spec, (int, float)):
                return float(amount_spec)
            
            if isinstance(amount_spec, str):
                if '%' in amount_spec:
                    # Percentage-based amount calculation
                    # This would need balance information and risk management
                    # For now, raise an error as it's not implemented
                    raise ValueError("Percentage-based amounts not yet implemented")
                else:
                    return float(amount_spec)
            
            return float(amount_spec)
        
        except Exception as e:
            raise ValueError(f"Invalid amount specification: {amount_spec}")


class OrderFactory:
    """Factory for creating order executors."""
    
    def __init__(self):
        self.executors = {}
        self._register_executors()
    
    def _register_executors(self):
        """Register all available order executors."""
        from .basic_orders import MarketOrderExecutor, LimitOrderExecutor, StopOrderExecutor
        
        self.executors = {
            'market': MarketOrderExecutor,
            'limit': LimitOrderExecutor,
            'stop': StopOrderExecutor,
            # Will add more order types here
        }
    
    def create_executor(self, order_type: str, connector: BaseExchangeConnector) -> Optional[OrderExecutor]:
        """Create an order executor for the specified order type."""
        executor_class = self.executors.get(order_type.lower())
        if not executor_class:
            logger.error("Unknown order type", order_type=order_type)
            return None
        
        return executor_class(connector)
    
    def get_supported_order_types(self) -> list:
        """Get list of supported order types."""
        return list(self.executors.keys())


# Global order factory instance
order_factory = OrderFactory()


def get_order_factory() -> OrderFactory:
    """Get the global order factory instance."""
    return order_factory
