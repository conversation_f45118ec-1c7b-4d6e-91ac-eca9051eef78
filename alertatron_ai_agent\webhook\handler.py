"""
Main webhook handler for processing TradingView signals.
"""

from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

from ..utils.logger import get_logger, log_webhook_received, log_error
from ..config.api_keys import get_api_key_manager
from ..exchanges.exchange_manager import get_exchange_manager
from ..orders.order_factory import get_order_factory
from .parser import CommandParser, ParsedCommand


logger = get_logger("webhook_handler")


class WebhookHandler:
    """Handles incoming TradingView webhooks and routes them for execution."""
    
    def __init__(self):
        self.parser = CommandParser()
        self.api_key_manager = get_api_key_manager()
        self.exchange_manager = get_exchange_manager()

        # Will be initialized when order executors are implemented
        self.order_executor = None
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an incoming webhook.
        
        Args:
            webhook_data: Raw webhook data from TradingView
            
        Returns:
            Processing result dictionary
        """
        try:
            # Extract message from webhook data
            message = webhook_data.get('message', '')
            if not message:
                raise ValueError("No message found in webhook data")
            
            # Parse the webhook message
            parsed_command = self.parse_webhook_message(webhook_data)
            if not parsed_command:
                raise ValueError("Failed to parse webhook message")
            
            # Log webhook reception
            log_webhook_received(
                source="tradingview",
                symbol=parsed_command.symbol,
                command=parsed_command.command,
                key_name=parsed_command.key_name,
                raw_message=parsed_command.raw_message
            )
            
            # Determine target exchange for the API key
            target_exchange = self._find_exchange_for_key(parsed_command.key_name)
            if not target_exchange:
                raise ValueError(f"No exchange found for API key: {parsed_command.key_name}")
            
            # Get the exchange connector
            connector = self.exchange_manager.get_connector(target_exchange, parsed_command.key_name)
            if not connector:
                raise ValueError(f"No connector found for {target_exchange}:{parsed_command.key_name}")

            # Process the command
            result = await self._execute_command(parsed_command, connector)
            
            return {
                "status": "success",
                "message": "Webhook processed successfully",
                "parsed_command": parsed_command.dict(),
                "target_exchange": target_exchange,
                "execution_result": result,
                "timestamp": datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            log_error(
                component="webhook_handler",
                error_type=type(e).__name__,
                error_message=str(e),
                webhook_data=webhook_data
            )
            
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def parse_webhook_message(self, webhook_data: Dict[str, Any]) -> Optional[ParsedCommand]:
        """Parse webhook message using the command parser."""
        message = webhook_data.get('message', '')
        return self.parser.parse_webhook_message(message)
    
    def _find_exchange_for_key(self, key_name: str) -> Optional[str]:
        """Find which exchange has the specified API key."""
        # Use exchange manager to find the connector
        connector = self.exchange_manager.find_connector_for_key(key_name)
        if connector:
            return connector.exchange_name
        return None
    
    async def _execute_command(self,
                             parsed_command: ParsedCommand,
                             connector) -> Dict[str, Any]:
        """
        Execute the parsed command using the exchange connector.

        This will be expanded as order types are implemented.
        """
        logger.info("Executing command",
                   command=parsed_command.command,
                   exchange=connector.exchange_name,
                   symbol=parsed_command.symbol)

        try:
            # Basic command execution - will be expanded with order types
            if parsed_command.command == 'market':
                return await self._execute_market_order(parsed_command, connector)
            elif parsed_command.command == 'limit':
                return await self._execute_limit_order(parsed_command, connector)
            elif parsed_command.command == 'stop':
                return await self._execute_stop_order(parsed_command, connector)
            else:
                return {
                    "status": "not_implemented",
                    "message": f"Order type {parsed_command.command} not yet implemented",
                    "symbol": parsed_command.symbol,
                    "parameters": parsed_command.parameters
                }

        except Exception as e:
            logger.error("Command execution failed",
                        command=parsed_command.command,
                        exchange=connector.exchange_name,
                        error=str(e))
            return {
                "status": "error",
                "message": f"Execution failed: {str(e)}",
                "symbol": parsed_command.symbol
            }

    async def _execute_market_order(self, parsed_command: ParsedCommand, connector) -> Dict[str, Any]:
        """Execute a market order."""
        params = parsed_command.parameters

        # Extract required parameters
        side = params.get('side', 'buy')
        amount = params.get('amount', 0)

        if not amount:
            raise ValueError("Amount is required for market orders")

        # TODO: Handle percentage amounts and position sizing
        if isinstance(amount, str) and '%' in amount:
            raise ValueError("Percentage amounts not yet implemented")

        from ..exchanges.base_connector import OrderSide
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL

        order = await connector.create_market_order(
            symbol=parsed_command.symbol,
            side=order_side,
            amount=float(amount)
        )

        return {
            "status": "success",
            "order_type": "market",
            "order": order.dict(),
            "message": f"Market order executed successfully"
        }

    async def _execute_limit_order(self, parsed_command: ParsedCommand, connector) -> Dict[str, Any]:
        """Execute a limit order."""
        params = parsed_command.parameters

        # Extract required parameters
        side = params.get('side', 'buy')
        amount = params.get('amount', 0)
        offset = params.get('offset')

        if not amount or not offset:
            raise ValueError("Amount and offset are required for limit orders")

        # TODO: Calculate price from offset
        # For now, assume offset is the actual price
        price = float(offset) if isinstance(offset, (int, float)) else 0
        if price == 0:
            raise ValueError("Price calculation from offset not yet implemented")

        from ..exchanges.base_connector import OrderSide
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL

        order = await connector.create_limit_order(
            symbol=parsed_command.symbol,
            side=order_side,
            amount=float(amount),
            price=price
        )

        return {
            "status": "success",
            "order_type": "limit",
            "order": order.dict(),
            "message": f"Limit order placed successfully"
        }

    async def _execute_stop_order(self, parsed_command: ParsedCommand, connector) -> Dict[str, Any]:
        """Execute a stop order."""
        params = parsed_command.parameters

        # Extract required parameters
        side = params.get('side', 'buy')
        amount = params.get('amount', 0)
        offset = params.get('offset')

        if not amount or not offset:
            raise ValueError("Amount and offset are required for stop orders")

        # TODO: Calculate stop price from offset
        stop_price = float(offset) if isinstance(offset, (int, float)) else 0
        if stop_price == 0:
            raise ValueError("Stop price calculation from offset not yet implemented")

        from ..exchanges.base_connector import OrderSide
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL

        order = await connector.create_stop_order(
            symbol=parsed_command.symbol,
            side=order_side,
            amount=float(amount),
            stop_price=stop_price
        )

        return {
            "status": "success",
            "order_type": "stop",
            "order": order.dict(),
            "message": f"Stop order placed successfully"
        }
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get webhook processing statistics."""
        # This would be implemented with actual metrics tracking
        return {
            "total_webhooks_processed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "supported_exchanges": len(self.api_key_manager.list_available_keys()),
            "status": "Handler initialized - exchange connectors pending"
        }
    
    async def test_webhook_processing(self, test_message: str) -> Dict[str, Any]:
        """Test webhook processing with a sample message."""
        test_webhook_data = {
            "message": test_message,
            "timestamp": datetime.utcnow().isoformat(),
            "source": "test"
        }
        
        return await self.process_webhook(test_webhook_data)
