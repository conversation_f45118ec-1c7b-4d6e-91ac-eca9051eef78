"""
Main webhook handler for processing TradingView signals.
"""

from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

from ..utils.logger import get_logger, log_webhook_received, log_error
from ..config.api_keys import get_api_key_manager
from ..exchanges.exchange_manager import get_exchange_manager
from ..orders.order_factory import get_order_factory
from ..risk_management.risk_calculator import get_risk_calculator
from ..risk_management.position_manager import get_position_manager
from .parser import CommandParser, ParsedCommand


logger = get_logger("webhook_handler")


class WebhookHandler:
    """Handles incoming TradingView webhooks and routes them for execution."""
    
    def __init__(self):
        self.parser = CommandParser()
        self.api_key_manager = get_api_key_manager()
        self.exchange_manager = get_exchange_manager()
        self.order_factory = get_order_factory()
        self.risk_calculator = get_risk_calculator()
        self.position_manager = get_position_manager()
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an incoming webhook.
        
        Args:
            webhook_data: Raw webhook data from TradingView
            
        Returns:
            Processing result dictionary
        """
        try:
            # Extract message from webhook data
            message = webhook_data.get('message', '')
            if not message:
                raise ValueError("No message found in webhook data")
            
            # Parse the webhook message
            parsed_command = self.parse_webhook_message(webhook_data)
            if not parsed_command:
                raise ValueError("Failed to parse webhook message")
            
            # Log webhook reception
            log_webhook_received(
                source="tradingview",
                symbol=parsed_command.symbol,
                command=parsed_command.command,
                key_name=parsed_command.key_name,
                raw_message=parsed_command.raw_message
            )
            
            # Determine target exchange for the API key
            target_exchange = self._find_exchange_for_key(parsed_command.key_name)
            if not target_exchange:
                raise ValueError(f"No exchange found for API key: {parsed_command.key_name}")
            
            # Get the exchange connector
            connector = self.exchange_manager.get_connector(target_exchange, parsed_command.key_name)
            if not connector:
                raise ValueError(f"No connector found for {target_exchange}:{parsed_command.key_name}")

            # Perform risk checks before execution
            risk_check = await self._perform_risk_checks(parsed_command, connector)
            if not risk_check["allowed"]:
                raise ValueError(f"Risk check failed: {risk_check['reason']}")

            # Process the command
            result = await self._execute_command(parsed_command, connector)
            
            return {
                "status": "success",
                "message": "Webhook processed successfully",
                "parsed_command": parsed_command.dict(),
                "target_exchange": target_exchange,
                "execution_result": result,
                "timestamp": datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            log_error(
                component="webhook_handler",
                error_type=type(e).__name__,
                error_message=str(e),
                webhook_data=webhook_data
            )
            
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def parse_webhook_message(self, webhook_data: Dict[str, Any]) -> Optional[ParsedCommand]:
        """Parse webhook message using the command parser."""
        message = webhook_data.get('message', '')
        return self.parser.parse_webhook_message(message)
    
    def _find_exchange_for_key(self, key_name: str) -> Optional[str]:
        """Find which exchange has the specified API key."""
        # Use exchange manager to find the connector
        connector = self.exchange_manager.find_connector_for_key(key_name)
        if connector:
            return connector.exchange_name
        return None
    
    async def _execute_command(self,
                             parsed_command: ParsedCommand,
                             connector) -> Dict[str, Any]:
        """
        Execute the parsed command using the exchange connector.

        This will be expanded as order types are implemented.
        """
        logger.info("Executing command",
                   command=parsed_command.command,
                   exchange=connector.exchange_name,
                   symbol=parsed_command.symbol)

        try:
            # Use order factory to create appropriate executor
            executor = self.order_factory.create_executor(parsed_command.command, connector)
            if not executor:
                return {
                    "status": "not_implemented",
                    "message": f"Order type {parsed_command.command} not yet implemented",
                    "symbol": parsed_command.symbol,
                    "parameters": parsed_command.parameters,
                    "supported_types": self.order_factory.get_supported_order_types()
                }

            # Execute the order
            result = await executor.execute(parsed_command)
            return result

        except Exception as e:
            logger.error("Command execution failed",
                        command=parsed_command.command,
                        exchange=connector.exchange_name,
                        error=str(e))
            return {
                "status": "error",
                "message": f"Execution failed: {str(e)}",
                "symbol": parsed_command.symbol
            }


    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get webhook processing statistics."""
        # This would be implemented with actual metrics tracking
        return {
            "total_webhooks_processed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "supported_exchanges": len(self.api_key_manager.list_available_keys()),
            "status": "Handler initialized - exchange connectors pending"
        }
    
    async def test_webhook_processing(self, test_message: str) -> Dict[str, Any]:
        """Test webhook processing with a sample message."""
        test_webhook_data = {
            "message": test_message,
            "timestamp": datetime.utcnow().isoformat(),
            "source": "test"
        }
        
        return await self.process_webhook(test_webhook_data)

    async def _perform_risk_checks(self, parsed_command: ParsedCommand, connector) -> Dict[str, Any]:
        """Perform comprehensive risk checks before order execution."""
        try:
            # Check position limits
            position_limit_check = self.position_manager.check_position_limits(
                parsed_command.symbol, connector.exchange_name
            )
            if not position_limit_check["allowed"]:
                return position_limit_check

            # Check daily loss limits if this could result in a loss
            params = parsed_command.parameters
            if 'amount' in params:
                try:
                    current_price = await connector.get_ticker(parsed_command.symbol)
                    price = float(current_price.get('last', 0))
                    amount = float(params['amount']) if isinstance(params['amount'], (int, float)) else 100

                    # Estimate potential loss (simplified)
                    potential_loss = amount * price * 0.1  # Assume max 10% loss

                    daily_loss_check = self.risk_calculator.check_daily_loss_limit(
                        connector.exchange_name, potential_loss
                    )
                    if not daily_loss_check["valid"]:
                        return {
                            "allowed": False,
                            "reason": daily_loss_check["reason"],
                            "message": daily_loss_check["message"]
                        }
                except:
                    pass  # Skip if we can't calculate

            # Validate position size
            if 'amount' in params and parsed_command.command in ['market', 'limit']:
                try:
                    current_price = await connector.get_ticker(parsed_command.symbol)
                    price = float(current_price.get('last', 0))
                    amount = float(params['amount']) if isinstance(params['amount'], (int, float)) else 100

                    size_check = self.risk_calculator.validate_position_size(
                        parsed_command.symbol, amount, price
                    )
                    if not size_check["valid"]:
                        return {
                            "allowed": False,
                            "reason": size_check["reason"],
                            "message": size_check["message"]
                        }
                except:
                    pass  # Skip if we can't validate

            return {
                "allowed": True,
                "message": "All risk checks passed"
            }

        except Exception as e:
            logger.error("Risk check failed", error=str(e))
            return {
                "allowed": False,
                "reason": "risk_check_error",
                "message": f"Risk check error: {str(e)}"
            }
