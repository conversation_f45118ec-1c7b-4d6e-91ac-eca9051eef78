"""
Base exchange connector interface for unified trading operations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel
from enum import Enum
import ccxt
import asyncio

from ..utils.logger import get_logger
from ..config.api_keys import ExchangeCredentials


logger = get_logger("base_connector")


class OrderSide(str, Enum):
    """Order side enumeration."""
    BUY = "buy"
    SELL = "sell"


class OrderType(str, Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(str, Enum):
    """Order status enumeration."""
    PENDING = "pending"
    OPEN = "open"
    CLOSED = "closed"
    CANCELED = "canceled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class Position(BaseModel):
    """Position information model."""
    symbol: str
    side: str
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    percentage: float


class Order(BaseModel):
    """Order information model."""
    id: str
    symbol: str
    type: str
    side: str
    amount: float
    price: Optional[float] = None
    status: str
    filled: float = 0.0
    remaining: float = 0.0
    timestamp: int
    
    class Config:
        extra = "allow"


class Balance(BaseModel):
    """Balance information model."""
    currency: str
    free: float
    used: float
    total: float


class BaseExchangeConnector(ABC):
    """Base class for all exchange connectors."""
    
    def __init__(self, credentials: ExchangeCredentials, exchange_config: Dict[str, Any] = None):
        """
        Initialize the exchange connector.
        
        Args:
            credentials: Exchange API credentials
            exchange_config: Exchange-specific configuration
        """
        self.credentials = credentials
        self.config = exchange_config or {}
        self.exchange = None
        self.is_connected = False
        self.exchange_name = self.__class__.__name__.replace('Connector', '').lower()
        
        self._initialize_exchange()
    
    @abstractmethod
    def _initialize_exchange(self):
        """Initialize the CCXT exchange instance."""
        pass
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        Connect to the exchange and validate credentials.
        
        Returns:
            bool: True if connection successful
        """
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Disconnect from the exchange."""
        pass
    
    @abstractmethod
    async def get_balance(self) -> Dict[str, Balance]:
        """
        Get account balance.
        
        Returns:
            Dict[str, Balance]: Balance information by currency
        """
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Position]:
        """
        Get open positions (for futures/margin trading).
        
        Returns:
            List[Position]: List of open positions
        """
        pass
    
    @abstractmethod
    async def create_market_order(self, 
                                symbol: str, 
                                side: OrderSide, 
                                amount: float,
                                **kwargs) -> Order:
        """
        Create a market order.
        
        Args:
            symbol: Trading symbol
            side: Order side (buy/sell)
            amount: Order amount
            **kwargs: Additional parameters
            
        Returns:
            Order: Created order information
        """
        pass
    
    @abstractmethod
    async def create_limit_order(self, 
                               symbol: str, 
                               side: OrderSide, 
                               amount: float, 
                               price: float,
                               **kwargs) -> Order:
        """
        Create a limit order.
        
        Args:
            symbol: Trading symbol
            side: Order side (buy/sell)
            amount: Order amount
            price: Limit price
            **kwargs: Additional parameters
            
        Returns:
            Order: Created order information
        """
        pass
    
    @abstractmethod
    async def create_stop_order(self, 
                              symbol: str, 
                              side: OrderSide, 
                              amount: float, 
                              stop_price: float,
                              **kwargs) -> Order:
        """
        Create a stop order.
        
        Args:
            symbol: Trading symbol
            side: Order side (buy/sell)
            amount: Order amount
            stop_price: Stop trigger price
            **kwargs: Additional parameters
            
        Returns:
            Order: Created order information
        """
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """
        Cancel an order.
        
        Args:
            order_id: Order ID to cancel
            symbol: Trading symbol
            
        Returns:
            bool: True if cancellation successful
        """
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str, symbol: str) -> Order:
        """
        Get order status.
        
        Args:
            order_id: Order ID
            symbol: Trading symbol
            
        Returns:
            Order: Order information
        """
        pass
    
    @abstractmethod
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        Get ticker information.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dict[str, Any]: Ticker data
        """
        pass
    
    async def get_symbols(self) -> List[str]:
        """Get available trading symbols."""
        try:
            if not self.exchange:
                await self.connect()
            
            markets = await self.exchange.load_markets()
            return list(markets.keys())
        
        except Exception as e:
            logger.error("Error getting symbols", 
                        exchange=self.exchange_name, 
                        error=str(e))
            return []
    
    def _convert_ccxt_order(self, ccxt_order: Dict[str, Any]) -> Order:
        """Convert CCXT order format to internal Order model."""
        return Order(
            id=ccxt_order.get('id', ''),
            symbol=ccxt_order.get('symbol', ''),
            type=ccxt_order.get('type', ''),
            side=ccxt_order.get('side', ''),
            amount=float(ccxt_order.get('amount', 0)),
            price=float(ccxt_order.get('price', 0)) if ccxt_order.get('price') else None,
            status=ccxt_order.get('status', ''),
            filled=float(ccxt_order.get('filled', 0)),
            remaining=float(ccxt_order.get('remaining', 0)),
            timestamp=int(ccxt_order.get('timestamp', 0))
        )
    
    def _handle_exchange_error(self, error: Exception, operation: str):
        """Handle and log exchange errors."""
        error_type = type(error).__name__
        error_message = str(error)
        
        logger.error("Exchange operation failed",
                    exchange=self.exchange_name,
                    operation=operation,
                    error_type=error_type,
                    error_message=error_message)
        
        # Re-raise with additional context
        raise Exception(f"{self.exchange_name} {operation} failed: {error_message}")
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test exchange connection and return status."""
        try:
            await self.connect()
            balance = await self.get_balance()
            
            return {
                "connected": True,
                "exchange": self.exchange_name,
                "balance_currencies": len(balance),
                "status": "Connection successful"
            }
        
        except Exception as e:
            return {
                "connected": False,
                "exchange": self.exchange_name,
                "error": str(e),
                "status": "Connection failed"
            }
