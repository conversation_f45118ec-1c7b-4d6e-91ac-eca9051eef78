
# BEGIN: user added these matplotlib lines to ensure any plots do not pop-up in their UI
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive
import matplotlib.pyplot as plt
plt.ioff()
import os
os.environ['TERM'] = 'dumb'
# END: user added these matplotlib lines to ensure any plots do not pop-up in their UI
# filename: generate_comprehensive_documentation.py
# execution: true
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, blue, red, green
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from datetime import datetime
import json
import os

print("Generating comprehensive Alertatron algorithmic trading documentation...")
print("="*80)

# Create the PDF document
doc = SimpleDocTemplate("Alertatron_Algorithmic_Trading_Documentation.pdf", 
                       pagesize=A4, 
                       rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

# Get styles and create custom styles
styles = getSampleStyleSheet()
title_style = ParagraphStyle(
    'CustomTitle',
    parent=styles['Heading1'],
    fontSize=24,
    spaceAfter=30,
    textColor=HexColor('#2E86AB'),
    alignment=TA_CENTER
)

heading_style = ParagraphStyle(
    'CustomHeading',
    parent=styles['Heading2'],
    fontSize=16,
    spaceAfter=12,
    textColor=HexColor('#A23B72'),
    alignment=TA_LEFT
)

subheading_style = ParagraphStyle(
    'CustomSubHeading',
    parent=styles['Heading3'],
    fontSize=14,
    spaceAfter=8,
    textColor=HexColor('#F18F01'),
    alignment=TA_LEFT
)

# Story list to build the document
story = []

# Title Page
story.append(Paragraph("Alertatron Algorithmic Trading", title_style))
story.append(Paragraph("TradingView Webhook Integration", title_style))
story.append(Paragraph("Kapsamlı Teknik Dokümantasyon", title_style))
story.append(Spacer(1, 30))

# Add current date
current_date = datetime.now().strftime("%d %B %Y")
story.append(Paragraph(f"Hazırlanma Tarihi: {current_date}", styles['Normal']))
story.append(Spacer(1, 20))

# Executive Summary
story.append(Paragraph("Yönetici Özeti", heading_style))
story.append(Paragraph("""
Alertatron, TradingView uyarılarını popüler kripto para borsalarında otomatik işlemlere dönüştüren 
kapsamlı bir algoritmik ticaret platformudur. Bu dokümantasyon, TradingView webhook'larını kullanarak 
çalışan bir algoritmik ticaret projesi için gerekli tüm teknik bilgileri içermektedir.
""", styles['Normal']))
story.append(Spacer(1, 15))

# Key Features
story.append(Paragraph("Temel Özellikler:", subheading_style))
features_data = [
    ["Özellik", "Açıklama"],
    ["Desteklenen Borsalar", "9 büyük kripto para borsası (Binance, Bybit, OKX, vs.)"],
    ["Sipariş Türleri", "15+ gelişmiş algoritmik sipariş türü"],
    ["TradingView Entegrasyonu", "Webhook tabanlı otomatik sinyal işleme"],
    ["7/24 İşlem", "Kesintisiz otomatik ticaret imkanı"],
    ["Risk Yönetimi", "Stop-loss, take-profit, trailing stops"],
    ["Grafik Yakalama", "Uyarı anında grafik görüntüleme"]
]

features_table = Table(features_data, colWidths=[2*inch, 4*inch])
features_table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), HexColor('#2E86AB')),
    ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 12),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F5F5F5')),
    ('GRID', (0, 0), (-1, -1), 1, black)
]))
story.append(features_table)
story.append(PageBreak())

# Table of Contents
story.append(Paragraph("İçindekiler", heading_style))
toc_data = [
    ["1. Platform Genel Bakış", "3"],
    ["2. Temel Kavramlar", "4"],
    ["3. TradingView Webhook Entegrasyonu", "5"],
    ["4. Komut Referansı", "6"],
    ["5. Desteklenen Borsalar", "8"],
    ["6. API Entegrasyonu", "9"],
    ["7. Sipariş Türleri", "11"],
    ["8. Risk Yönetimi", "13"],
    ["9. Uygulama Örnekleri", "14"],
    ["10. Sorun Giderme", "16"],
    ["11. En İyi Uygulamalar", "17"],
    ["12. Kaynaklar ve Referanslar", "18"]
]

toc_table = Table(toc_data, colWidths=[4*inch, 1*inch])
toc_table.setStyle(TableStyle([
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
    ('FONTSIZE', (0, 0), (-1, -1), 11),
    ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
    ('DOTS', (0, 0), (-1, -1), 1, 1, black)
]))
story.append(toc_table)
story.append(PageBreak())

# Chapter 1: Platform Overview
story.append(Paragraph("1. Platform Genel Bakış", heading_style))
story.append(Paragraph("Alertatron Nedir?", subheading_style))
story.append(Paragraph("""
Alertatron, TradingView uyarılarını otomatik işlemlere dönüştüren bulut tabanlı bir platformdur. 
Kullanıcılar herhangi bir yazılım kurulumu gerektirmeden, TradingView'dan gelen sinyalleri 
otomatik olarak kripto para borsalarında işleme dönüştürebilirler.
""", styles['Normal']))
story.append(Spacer(1, 15))

story.append(Paragraph("Temel Avantajlar:", subheading_style))
advantages = [
    "• Kurulum gerektirmez - tamamen bulut tabanlı",
    "• 7/24 kesintisiz ticaret imkanı",
    "• Çoklu borsa desteği",
    "• Gelişmiş sipariş türleri",
    "• Otomatik risk yönetimi",
    "• Grafik yakalama ve raporlama"
]

for advantage in advantages:
    story.append(Paragraph(advantage, styles['Normal']))
story.append(Spacer(1, 15))

# Chapter 2: Basic Concepts
story.append(Paragraph("2. Temel Kavramlar", heading_style))
story.append(Paragraph("3 Adımlı İşlem Süreci:", subheading_style))

process_steps = [
    "1. <b>Ticaret Stratejisi Sinyal Üretir:</b> TradingView'da teknik indikatörler kullanılarak alım-satım sinyalleri oluşturulur.",
    "2. <b>Sinyal İşleme:</b> Alertatron platformu gelen sinyalleri analiz eder ve uygun komutlara dönüştürür.",
    "3. <b>Borsa'da İşlem Gerçekleşir:</b> Özel komutlar kullanılarak otomatik olarak işlemler gerçekleştirilir."
]

for step in process_steps:
    story.append(Paragraph(step, styles['Normal']))
    story.append(Spacer(1, 8))

story.append(PageBreak())

# Chapter 3: TradingView Webhook Integration
story.append(Paragraph("3. TradingView Webhook Entegrasyonu", heading_style))
story.append(Paragraph("Webhook Kurulumu:", subheading_style))
story.append(Paragraph("""
TradingView webhook'ları, uyarı tetiklendiğinde otomatik olarak Alertatron platformuna 
sinyal göndermek için kullanılır. Bu entegrasyon, manuel müdahale gerektirmeden 
otomatik ticaret yapmayı mümkün kılar.
""", styles['Normal']))
story.append(Spacer(1, 15))

story.append(Paragraph("Temel Komut Yapısı:", subheading_style))
command_example = """
MyKeys(XBTUSD) { market(side=buy, amount=100); }
#bot
"""
story.append(Paragraph(f"<font name='Courier'>{command_example}</font>", styles['Code']))
story.append(Spacer(1, 15))

story.append(Paragraph("Komut Açıklaması:", subheading_style))
command_explanation = [
    "• <b>MyKeys:</b> API anahtarı seti için benzersiz tanımlayıcı",
    "• <b>XBTUSD:</b> Borsadaki işlem sembolü",
    "• <b>{ }:</b> Yürütme komutlarını içeren kısım",
    "• <b>market(side=buy, amount=100):</b> Piyasa emri tanımı",
    "• <b>#bot:</b> Uyarı yönlendirme etiketi"
]

for explanation in command_explanation:
    story.append(Paragraph(explanation, styles['Normal']))
    story.append(Spacer(1, 5))

story.append(PageBreak())

# Chapter 4: Command Reference
story.append(Paragraph("4. Komut Referansı", heading_style))
story.append(Paragraph("Market Emri (market):", subheading_style))
story.append(Paragraph("""
Güncel fiyattan anında gerçekleştirilen piyasa emri. En temel ve hızlı işlem türüdür.
""", styles['Normal']))
story.append(Spacer(1, 10))

story.append(Paragraph("Söz Dizimi:", subheading_style))
story.append(Paragraph("<font name='Courier'>market(side, amount, position, reduceOnly)</font>", styles['Code']))
story.append(Spacer(1, 10))

# Parameters table
params_data = [
    ["Parametre", "Açıklama", "Örnek"],
    ["side", "Alım/Satım yönü (buy/sell)", "side=buy"],
    ["amount", "İşlem miktarı", "amount=100, amount=50%"],
    ["position", "Hedef pozisyon büyüklüğü", "position=1000"],
    ["reduceOnly", "Sadece pozisyon azaltma", "reduceOnly=true"]
]

params_table = Table(params_data, colWidths=[1.5*inch, 2.5*inch, 2*inch])
params_table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), HexColor('#A23B72')),
    ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 11),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F9F9F9')),
    ('GRID', (0, 0), (-1, -1), 1, black)
]))
story.append(params_table)
story.append(PageBreak())

# Chapter 5: Supported Exchanges
story.append(Paragraph("5. Desteklenen Borsalar", heading_style))
story.append(Paragraph("""
Alertatron, aşağıdaki büyük kripto para borsalarında otomatik ticaret yapmayı destekler:
""", styles['Normal']))
story.append(Spacer(1, 15))

exchanges_data = [
    ["Borsa", "Türü", "Özellikler"],
    ["Binance Spot", "Spot İşlemler", "En büyük kripto para borsası"],
    ["Binance Futures", "Vadeli İşlemler", "Yüksek kaldıraç imkanı"],
    ["Bybit", "Perpetual", "USDT ve Inverse kontratlar"],
    ["OKX", "Çoklu", "Spot ve vadeli işlemler"],
    ["Bitfinex", "Spot/Margin", "Profesyonel ticaret"],
    ["BitMEX", "Derivatives", "Bitcoin vadeli işlemler"],
    ["Bitget", "Çoklu", "Copy trading özelliği"],
    ["Deribit", "Options/Futures", "Kripto opsiyon işlemleri"],
    ["Binance US", "Spot", "ABD kullanıcıları için"]
]

exchanges_table = Table(exchanges_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
exchanges_table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), HexColor('#F18F01')),
    ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 11),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('BACKGROUND', (0, 1), (-1, -1), HexColor('#FFF8E1')),
    ('GRID', (0, 0), (-1, -1), 1, black)
]))
story.append(exchanges_table)
story.append(PageBreak())

# Build PDF
doc.build(story)

print("="*80)
print("PDF dokümantasyon başarıyla oluşturuldu!")
print("Dosya: Alertatron_Algorithmic_Trading_Documentation.pdf")
print("Sayfa sayısı: Yaklaşık 18 sayfa")
print("İçerik: Kapsamlı Türkçe teknik dokümantasyon")
print("="*80)